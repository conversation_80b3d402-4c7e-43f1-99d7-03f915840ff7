# 简化预约自动化系统设计方案

## 1. 系统概述

基于现有SeatMaster系统，通过MySQL数据库存储用户预约配置信息，让Python脚本自动执行预约操作。保持架构简单，避免复杂的分布式设计。

### 1.1 设计目标
- 在现有系统基础上最小化改动
- 通过数据库驱动Python脚本执行预约
- 简单可靠，易于维护和调试
- 支持多用户自动预约配置

### 1.2 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web管理界面    │    │   MySQL数据库    │    │  Python预约脚本  │
│  (现有Vue前端)   │◄──►│  (存储预约配置)  │◄──►│ (@reservationXue │
│                │    │                │    │  xitong文件夹)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Spring Boot    │    │   预约配置表     │    │   定时任务调度   │
│   后端API       │    │   执行日志表     │    │   (cron/定时器)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 数据库设计

### 2.1 预约配置表 (auto_reservation_configs)

```sql
CREATE TABLE auto_reservation_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    target_system VARCHAR(50) NOT NULL COMMENT '目标系统(xuexitong等)',

    -- 预约时间配置
    reservation_time TIME NOT NULL COMMENT '预约执行时间(如08:00:00)',
    advance_days INT DEFAULT 1 COMMENT '提前预约天数',

    -- 座位偏好配置
    preferred_room_ids TEXT COMMENT '偏好房间ID列表(JSON格式)',
    preferred_seat_types VARCHAR(100) COMMENT '偏好座位类型(window,aisle,center)',
    avoid_seat_ids TEXT COMMENT '避免的座位ID列表(JSON格式)',

    -- 用户凭证信息
    username VARCHAR(100) NOT NULL COMMENT '目标系统用户名',
    password_encrypted TEXT NOT NULL COMMENT '加密后的密码',
    additional_params TEXT COMMENT '额外参数(JSON格式)',

    -- 执行配置
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    retry_count INT DEFAULT 3 COMMENT '失败重试次数',
    retry_interval INT DEFAULT 5 COMMENT '重试间隔(分钟)',

    -- 时间字段
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_executed_time TIMESTAMP NULL COMMENT '最后执行时间',

    -- 索引
    INDEX idx_user_enabled (user_id, is_enabled),
    INDEX idx_reservation_time (reservation_time, is_enabled),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 2.2 执行日志表 (auto_reservation_logs)

```sql
CREATE TABLE auto_reservation_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_id BIGINT NOT NULL COMMENT '配置ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',

    -- 执行信息
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    target_date DATE NOT NULL COMMENT '预约目标日期',
    execution_status ENUM('SUCCESS', 'FAILED', 'PARTIAL', 'SKIPPED') NOT NULL,

    -- 结果信息
    reserved_room_id BIGINT NULL COMMENT '成功预约的房间ID',
    reserved_seat_id BIGINT NULL COMMENT '成功预约的座位ID',
    error_message TEXT NULL COMMENT '错误信息',
    execution_details TEXT NULL COMMENT '执行详情(JSON格式)',

    -- 性能信息
    execution_duration INT NULL COMMENT '执行耗时(秒)',
    retry_count INT DEFAULT 0 COMMENT '重试次数',

    -- 索引
    INDEX idx_config_time (config_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    INDEX idx_target_date (target_date),
    FOREIGN KEY (config_id) REFERENCES auto_reservation_configs(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### 2.3 系统配置表 (auto_reservation_settings)

```sql
CREATE TABLE auto_reservation_settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description VARCHAR(200),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认配置
INSERT INTO auto_reservation_settings (setting_key, setting_value, description) VALUES
('python_script_path', '/path/to/@reservationXuexitong', 'Python脚本路径'),
('max_concurrent_executions', '5', '最大并发执行数'),
('default_timeout', '300', '默认超时时间(秒)'),
('log_retention_days', '30', '日志保留天数'),
('notification_enabled', 'true', '是否启用通知');
```

## 3. 后端API扩展

### 3.1 配置管理Controller

```java
@RestController
@RequestMapping("/api/auto-reservation")
public class AutoReservationController {

    @Autowired
    private AutoReservationService autoReservationService;

    // 获取用户的预约配置列表
    @GetMapping("/configs")
    public ResponseEntity<List<AutoReservationConfig>> getUserConfigs() {
        Long userId = getCurrentUserId();
        List<AutoReservationConfig> configs = autoReservationService.getUserConfigs(userId);
        return ResponseEntity.ok(configs);
    }

    // 创建预约配置
    @PostMapping("/configs")
    public ResponseEntity<AutoReservationConfig> createConfig(
            @RequestBody @Valid CreateConfigRequest request) {
        Long userId = getCurrentUserId();
        AutoReservationConfig config = autoReservationService.createConfig(userId, request);
        return ResponseEntity.ok(config);
    }

    // 更新预约配置
    @PutMapping("/configs/{configId}")
    public ResponseEntity<AutoReservationConfig> updateConfig(
            @PathVariable Long configId,
            @RequestBody @Valid UpdateConfigRequest request) {
        Long userId = getCurrentUserId();
        AutoReservationConfig config = autoReservationService.updateConfig(userId, configId, request);
        return ResponseEntity.ok(config);
    }

    // 启用/禁用配置
    @PutMapping("/configs/{configId}/toggle")
    public ResponseEntity<Void> toggleConfig(@PathVariable Long configId) {
        Long userId = getCurrentUserId();
        autoReservationService.toggleConfig(userId, configId);
        return ResponseEntity.ok().build();
    }

    // 删除配置
    @DeleteMapping("/configs/{configId}")
    public ResponseEntity<Void> deleteConfig(@PathVariable Long configId) {
        Long userId = getCurrentUserId();
        autoReservationService.deleteConfig(userId, configId);
        return ResponseEntity.ok().build();
    }

    // 获取执行日志
    @GetMapping("/configs/{configId}/logs")
    public ResponseEntity<PageResult<AutoReservationLog>> getConfigLogs(
            @PathVariable Long configId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        Long userId = getCurrentUserId();
        PageResult<AutoReservationLog> logs = autoReservationService.getConfigLogs(userId, configId, page, size);
        return ResponseEntity.ok(logs);
    }

    // 手动触发执行
    @PostMapping("/configs/{configId}/execute")
    public ResponseEntity<ExecutionResult> manualExecute(@PathVariable Long configId) {
        Long userId = getCurrentUserId();
        ExecutionResult result = autoReservationService.manualExecute(userId, configId);
        return ResponseEntity.ok(result);
    }

    // 测试配置
    @PostMapping("/configs/test")
    public ResponseEntity<TestResult> testConfig(@RequestBody @Valid TestConfigRequest request) {
        TestResult result = autoReservationService.testConfig(request);
        return ResponseEntity.ok(result);
    }
}
```

### 3.2 Service层实现

```java
@Service
@Transactional
public class AutoReservationService {

    @Autowired
    private AutoReservationConfigRepository configRepository;

    @Autowired
    private AutoReservationLogRepository logRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private PythonScriptExecutor pythonExecutor;

    public List<AutoReservationConfig> getUserConfigs(Long userId) {
        return configRepository.findByUserIdOrderByCreatedTimeDesc(userId);
    }

    public AutoReservationConfig createConfig(Long userId, CreateConfigRequest request) {
        // 验证用户权限和配置合法性
        validateConfigRequest(request);

        AutoReservationConfig config = new AutoReservationConfig();
        config.setUserId(userId);
        config.setConfigName(request.getConfigName());
        config.setTargetSystem(request.getTargetSystem());
        config.setReservationTime(request.getReservationTime());
        config.setAdvanceDays(request.getAdvanceDays());
        config.setPreferredRoomIds(JsonUtils.toJson(request.getPreferredRoomIds()));
        config.setPreferredSeatTypes(request.getPreferredSeatTypes());
        config.setAvoidSeatIds(JsonUtils.toJson(request.getAvoidSeatIds()));
        config.setUsername(request.getUsername());

        // 加密密码
        config.setPasswordEncrypted(passwordEncoder.encode(request.getPassword()));
        config.setAdditionalParams(JsonUtils.toJson(request.getAdditionalParams()));

        config.setRetryCount(request.getRetryCount());
        config.setRetryInterval(request.getRetryInterval());
        config.setEnabled(true);

        return configRepository.save(config);
    }

    public AutoReservationConfig updateConfig(Long userId, Long configId, UpdateConfigRequest request) {
        AutoReservationConfig config = configRepository.findByIdAndUserId(configId, userId)
            .orElseThrow(() -> new BusinessException("配置不存在"));

        // 更新配置字段
        if (request.getConfigName() != null) {
            config.setConfigName(request.getConfigName());
        }
        if (request.getReservationTime() != null) {
            config.setReservationTime(request.getReservationTime());
        }
        // ... 其他字段更新

        if (request.getPassword() != null) {
            config.setPasswordEncrypted(passwordEncoder.encode(request.getPassword()));
        }

        return configRepository.save(config);
    }

    public void toggleConfig(Long userId, Long configId) {
        AutoReservationConfig config = configRepository.findByIdAndUserId(configId, userId)
            .orElseThrow(() -> new BusinessException("配置不存在"));

        config.setEnabled(!config.getEnabled());
        configRepository.save(config);
    }

    public ExecutionResult manualExecute(Long userId, Long configId) {
        AutoReservationConfig config = configRepository.findByIdAndUserId(configId, userId)
            .orElseThrow(() -> new BusinessException("配置不存在"));

        // 调用Python脚本执行预约
        return pythonExecutor.executeReservation(config);
    }

    private void validateConfigRequest(CreateConfigRequest request) {
        // 验证时间格式
        if (request.getReservationTime() == null) {
            throw new BusinessException("预约时间不能为空");
        }

        // 验证房间ID
        if (request.getPreferredRoomIds() != null && !request.getPreferredRoomIds().isEmpty()) {
            // 验证房间ID是否存在
            List<Long> roomIds = request.getPreferredRoomIds();
            // ... 验证逻辑
        }

        // 验证用户名密码
        if (StringUtils.isBlank(request.getUsername()) || StringUtils.isBlank(request.getPassword())) {
            throw new BusinessException("用户名和密码不能为空");
        }
    }
}
```

## 4. Python脚本集成

### 4.1 脚本执行器

```java
@Component
public class PythonScriptExecutor {

    @Value("${auto-reservation.python.script-path}")
    private String scriptPath;

    @Value("${auto-reservation.python.timeout:300}")
    private int timeoutSeconds;

    @Autowired
    private AutoReservationLogRepository logRepository;

    public ExecutionResult executeReservation(AutoReservationConfig config) {
        AutoReservationLog log = new AutoReservationLog();
        log.setConfigId(config.getId());
        log.setUserId(config.getUserId());
        log.setTargetDate(LocalDate.now().plusDays(config.getAdvanceDays()));
        log.setExecutionTime(LocalDateTime.now());

        long startTime = System.currentTimeMillis();

        try {
            // 构建Python脚本参数
            List<String> command = buildPythonCommand(config);

            // 执行Python脚本
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.directory(new File(scriptPath));
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 读取输出
            String output = readProcessOutput(process);

            // 等待执行完成
            boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("脚本执行超时");
            }

            int exitCode = process.exitValue();
            long duration = (System.currentTimeMillis() - startTime) / 1000;

            // 解析执行结果
            ExecutionResult result = parseExecutionResult(output, exitCode);

            // 记录日志
            log.setExecutionStatus(result.isSuccess() ?
                AutoReservationLog.Status.SUCCESS : AutoReservationLog.Status.FAILED);
            log.setReservedRoomId(result.getReservedRoomId());
            log.setReservedSeatId(result.getReservedSeatId());
            log.setErrorMessage(result.getErrorMessage());
            log.setExecutionDetails(JsonUtils.toJson(result.getDetails()));
            log.setExecutionDuration((int) duration);

            logRepository.save(log);

            // 更新配置的最后执行时间
            config.setLastExecutedTime(LocalDateTime.now());

            return result;

        } catch (Exception e) {
            log.setExecutionStatus(AutoReservationLog.Status.FAILED);
            log.setErrorMessage(e.getMessage());
            log.setExecutionDuration((int) ((System.currentTimeMillis() - startTime) / 1000));
            logRepository.save(log);

            return ExecutionResult.failure(e.getMessage());
        }
    }

    private List<String> buildPythonCommand(AutoReservationConfig config) {
        List<String> command = new ArrayList<>();
        command.add("python");
        command.add("main.py"); // 主执行脚本

        // 添加参数
        command.add("--username");
        command.add(config.getUsername());

        command.add("--password");
        command.add(passwordEncoder.decode(config.getPasswordEncrypted()));

        command.add("--target-date");
        command.add(LocalDate.now().plusDays(config.getAdvanceDays()).toString());

        if (config.getPreferredRoomIds() != null) {
            command.add("--preferred-rooms");
            command.add(config.getPreferredRoomIds());
        }

        if (config.getPreferredSeatTypes() != null) {
            command.add("--seat-types");
            command.add(config.getPreferredSeatTypes());
        }

        if (config.getAvoidSeatIds() != null) {
            command.add("--avoid-seats");
            command.add(config.getAvoidSeatIds());
        }

        command.add("--retry-count");
        command.add(String.valueOf(config.getRetryCount()));

        command.add("--retry-interval");
        command.add(String.valueOf(config.getRetryInterval()));

        return command;
    }

    private String readProcessOutput(Process process) throws IOException {
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        return output.toString();
    }

    private ExecutionResult parseExecutionResult(String output, int exitCode) {
        // 解析Python脚本的输出结果
        // 假设Python脚本输出JSON格式的结果
        try {
            if (exitCode == 0) {
                // 成功情况，解析输出中的JSON结果
                Map<String, Object> result = JsonUtils.fromJson(output, Map.class);
                return ExecutionResult.success(
                    (Long) result.get("roomId"),
                    (Long) result.get("seatId"),
                    (Map<String, Object>) result.get("details")
                );
            } else {
                // 失败情况
                return ExecutionResult.failure(output);
            }
        } catch (Exception e) {
            return ExecutionResult.failure("解析执行结果失败: " + e.getMessage());
        }
    }
}
```

### 4.2 定时任务调度

```java
@Component
@EnableScheduling
public class AutoReservationScheduler {

    @Autowired
    private AutoReservationConfigRepository configRepository;

    @Autowired
    private PythonScriptExecutor pythonExecutor;

    @Autowired
    private TaskExecutor taskExecutor;

    // 每分钟检查一次是否有需要执行的预约任务
    @Scheduled(cron = "0 * * * * ?")
    public void checkAndExecuteReservations() {
        LocalTime currentTime = LocalTime.now();
        LocalTime timeWindow = currentTime.plusMinutes(1); // 1分钟的时间窗口

        // 查找需要在当前时间执行的配置
        List<AutoReservationConfig> configs = configRepository
            .findEnabledConfigsByTimeRange(currentTime, timeWindow);

        for (AutoReservationConfig config : configs) {
            // 检查是否已经在今天执行过
            if (isAlreadyExecutedToday(config)) {
                continue;
            }

            // 异步执行预约任务
            taskExecutor.execute(() -> {
                try {
                    pythonExecutor.executeReservation(config);
                } catch (Exception e) {
                    log.error("执行自动预约失败: configId={}, error={}",
                        config.getId(), e.getMessage(), e);
                }
            });
        }
    }

    private boolean isAlreadyExecutedToday(AutoReservationConfig config) {
        LocalDate today = LocalDate.now();
        LocalDate targetDate = today.plusDays(config.getAdvanceDays());

        return logRepository.existsByConfigIdAndTargetDateAndExecutionStatus(
            config.getId(), targetDate, AutoReservationLog.Status.SUCCESS);
    }

    // 每天凌晨清理过期日志
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredLogs() {
        int retentionDays = getLogRetentionDays();
        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(retentionDays);

        int deletedCount = logRepository.deleteByExecutionTimeBefore(cutoffTime);
        log.info("清理过期日志完成，删除记录数: {}", deletedCount);
    }

    private int getLogRetentionDays() {
        // 从系统配置中获取日志保留天数
        return 30; // 默认30天
    }
}
```

## 5. 前端界面扩展

### 5.1 自动预约配置页面

```vue
<!-- AutoReservationConfig.vue -->
<template>
  <div class="auto-reservation-config">
    <el-card header="自动预约配置">
      <!-- 配置列表 -->
      <el-table :data="configs" style="width: 100%">
        <el-table-column prop="configName" label="配置名称" width="150"/>
        <el-table-column prop="targetSystem" label="目标系统" width="100"/>
        <el-table-column prop="reservationTime" label="预约时间" width="100"/>
        <el-table-column prop="advanceDays" label="提前天数" width="100"/>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.enabled"
              @change="toggleConfig(scope.row)"
              active-text="启用"
              inactive-text="禁用">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="lastExecutedTime" label="最后执行" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.lastExecutedTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editConfig(scope.row)">编辑</el-button>
            <el-button size="small" @click="viewLogs(scope.row)">日志</el-button>
            <el-button size="small" type="primary" @click="manualExecute(scope.row)">
              手动执行
            </el-button>
            <el-button size="small" type="danger" @click="deleteConfig(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加配置按钮 -->
      <div style="margin-top: 20px;">
        <el-button type="primary" @click="showCreateDialog">添加配置</el-button>
      </div>
    </el-card>

    <!-- 创建/编辑配置对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm">

      <el-form :model="configForm" :rules="formRules" ref="configFormRef" label-width="120px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="configForm.configName" placeholder="请输入配置名称"/>
        </el-form-item>

        <el-form-item label="目标系统" prop="targetSystem">
          <el-select v-model="configForm.targetSystem" placeholder="请选择目标系统">
            <el-option label="学习通" value="xuexitong"/>
            <el-option label="其他系统" value="other"/>
          </el-select>
        </el-form-item>

        <el-form-item label="预约时间" prop="reservationTime">
          <el-time-picker
            v-model="configForm.reservationTime"
            format="HH:mm:ss"
            placeholder="选择预约执行时间"/>
        </el-form-item>

        <el-form-item label="提前天数" prop="advanceDays">
          <el-input-number
            v-model="configForm.advanceDays"
            :min="1"
            :max="7"
            placeholder="提前几天预约"/>
        </el-form-item>

        <el-form-item label="偏好房间">
          <el-select
            v-model="configForm.preferredRoomIds"
            multiple
            placeholder="选择偏好房间">
            <el-option
              v-for="room in availableRooms"
              :key="room.id"
              :label="room.name"
              :value="room.id"/>
          </el-select>
        </el-form-item>

        <el-form-item label="座位类型偏好">
          <el-checkbox-group v-model="configForm.preferredSeatTypes">
            <el-checkbox label="window">靠窗</el-checkbox>
            <el-checkbox label="aisle">靠过道</el-checkbox>
            <el-checkbox label="center">中间</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input v-model="configForm.username" placeholder="目标系统用户名"/>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="configForm.password"
            type="password"
            placeholder="目标系统密码"
            show-password/>
        </el-form-item>

        <el-form-item label="重试次数">
          <el-input-number
            v-model="configForm.retryCount"
            :min="1"
            :max="10"
            placeholder="失败重试次数"/>
        </el-form-item>

        <el-form-item label="重试间隔">
          <el-input-number
            v-model="configForm.retryInterval"
            :min="1"
            :max="60"
            placeholder="重试间隔(分钟)"/>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存</el-button>
        <el-button v-if="!isEditing" @click="testConfig">测试配置</el-button>
      </template>
    </el-dialog>

    <!-- 执行日志对话框 -->
    <el-dialog title="执行日志" v-model="logDialogVisible" width="800px">
      <el-table :data="logs" style="width: 100%">
        <el-table-column prop="executionTime" label="执行时间" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.executionTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="targetDate" label="目标日期" width="100"/>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.executionStatus)">
              {{ getStatusText(scope.row.executionStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reservedRoomId" label="预约房间" width="100"/>
        <el-table-column prop="reservedSeatId" label="预约座位" width="100"/>
        <el-table-column prop="executionDuration" label="耗时(秒)" width="80"/>
        <el-table-column prop="errorMessage" label="错误信息" show-overflow-tooltip/>
      </el-table>

      <el-pagination
        v-model:current-page="logPagination.page"
        v-model:page-size="logPagination.size"
        :total="logPagination.total"
        @current-change="loadLogs"
        layout="prev, pager, next, total"/>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { autoReservationApi } from '@/api/autoReservation'

export default {
  name: 'AutoReservationConfig',
  setup() {
    const configs = ref([])
    const availableRooms = ref([])
    const logs = ref([])

    const dialogVisible = ref(false)
    const logDialogVisible = ref(false)
    const isEditing = ref(false)
    const currentConfigId = ref(null)

    const configForm = reactive({
      configName: '',
      targetSystem: 'xuexitong',
      reservationTime: '',
      advanceDays: 1,
      preferredRoomIds: [],
      preferredSeatTypes: [],
      username: '',
      password: '',
      retryCount: 3,
      retryInterval: 5
    })

    const logPagination = reactive({
      page: 1,
      size: 20,
      total: 0
    })

    const formRules = {
      configName: [
        { required: true, message: '请输入配置名称', trigger: 'blur' }
      ],
      targetSystem: [
        { required: true, message: '请选择目标系统', trigger: 'change' }
      ],
      reservationTime: [
        { required: true, message: '请选择预约时间', trigger: 'change' }
      ],
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' }
      ]
    }

    // 加载配置列表
    const loadConfigs = async () => {
      try {
        const response = await autoReservationApi.getConfigs()
        configs.value = response.data
      } catch (error) {
        ElMessage.error('加载配置失败')
      }
    }

    // 加载可用房间
    const loadAvailableRooms = async () => {
      try {
        const response = await roomApi.getAllRooms()
        availableRooms.value = response.data
      } catch (error) {
        ElMessage.error('加载房间列表失败')
      }
    }

    // 显示创建对话框
    const showCreateDialog = () => {
      isEditing.value = false
      dialogVisible.value = true
      resetForm()
    }

    // 编辑配置
    const editConfig = (config) => {
      isEditing.value = true
      currentConfigId.value = config.id
      dialogVisible.value = true

      // 填充表单
      Object.assign(configForm, {
        configName: config.configName,
        targetSystem: config.targetSystem,
        reservationTime: config.reservationTime,
        advanceDays: config.advanceDays,
        preferredRoomIds: JSON.parse(config.preferredRoomIds || '[]'),
        preferredSeatTypes: config.preferredSeatTypes ? config.preferredSeatTypes.split(',') : [],
        username: config.username,
        password: '', // 不显示原密码
        retryCount: config.retryCount,
        retryInterval: config.retryInterval
      })
    }

    // 保存配置
    const saveConfig = async () => {
      try {
        if (isEditing.value) {
          await autoReservationApi.updateConfig(currentConfigId.value, configForm)
          ElMessage.success('更新配置成功')
        } else {
          await autoReservationApi.createConfig(configForm)
          ElMessage.success('创建配置成功')
        }

        dialogVisible.value = false
        loadConfigs()
      } catch (error) {
        ElMessage.error('保存配置失败')
      }
    }

    // 切换配置状态
    const toggleConfig = async (config) => {
      try {
        await autoReservationApi.toggleConfig(config.id)
        ElMessage.success(config.enabled ? '启用成功' : '禁用成功')
      } catch (error) {
        ElMessage.error('操作失败')
        config.enabled = !config.enabled // 回滚状态
      }
    }

    // 手动执行
    const manualExecute = async (config) => {
      try {
        ElMessage.info('开始执行预约...')
        const response = await autoReservationApi.manualExecute(config.id)

        if (response.data.success) {
          ElMessage.success('预约执行成功')
        } else {
          ElMessage.error('预约执行失败: ' + response.data.errorMessage)
        }

        loadConfigs()
      } catch (error) {
        ElMessage.error('执行失败')
      }
    }

    // 查看日志
    const viewLogs = async (config) => {
      currentConfigId.value = config.id
      logDialogVisible.value = true
      logPagination.page = 1
      await loadLogs()
    }

    // 加载日志
    const loadLogs = async () => {
      try {
        const response = await autoReservationApi.getConfigLogs(
          currentConfigId.value,
          logPagination.page,
          logPagination.size
        )
        logs.value = response.data.records
        logPagination.total = response.data.total
      } catch (error) {
        ElMessage.error('加载日志失败')
      }
    }

    // 删除配置
    const deleteConfig = async (config) => {
      try {
        await ElMessageBox.confirm('确定要删除这个配置吗？', '确认删除')
        await autoReservationApi.deleteConfig(config.id)
        ElMessage.success('删除成功')
        loadConfigs()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }

    // 测试配置
    const testConfig = async () => {
      try {
        ElMessage.info('正在测试配置...')
        const response = await autoReservationApi.testConfig(configForm)

        if (response.data.success) {
          ElMessage.success('配置测试成功')
        } else {
          ElMessage.error('配置测试失败: ' + response.data.errorMessage)
        }
      } catch (error) {
        ElMessage.error('测试失败')
      }
    }

    // 重置表单
    const resetForm = () => {
      Object.assign(configForm, {
        configName: '',
        targetSystem: 'xuexitong',
        reservationTime: '',
        advanceDays: 1,
        preferredRoomIds: [],
        preferredSeatTypes: [],
        username: '',
        password: '',
        retryCount: 3,
        retryInterval: 5
      })
    }

    // 工具方法
    const formatTime = (time) => {
      return time ? new Date(time).toLocaleString() : '-'
    }

    const getStatusType = (status) => {
      const typeMap = {
        'SUCCESS': 'success',
        'FAILED': 'danger',
        'PARTIAL': 'warning',
        'SKIPPED': 'info'
      }
      return typeMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const textMap = {
        'SUCCESS': '成功',
        'FAILED': '失败',
        'PARTIAL': '部分成功',
        'SKIPPED': '跳过'
      }
      return textMap[status] || status
    }

    const dialogTitle = computed(() => {
      return isEditing.value ? '编辑配置' : '创建配置'
    })

    onMounted(() => {
      loadConfigs()
      loadAvailableRooms()
    })

    return {
      configs,
      availableRooms,
      logs,
      dialogVisible,
      logDialogVisible,
      isEditing,
      configForm,
      logPagination,
      formRules,
      dialogTitle,

      showCreateDialog,
      editConfig,
      saveConfig,
      toggleConfig,
      manualExecute,
      viewLogs,
      loadLogs,
      deleteConfig,
      testConfig,
      resetForm,
      formatTime,
      getStatusType,
      getStatusText
    }
  }
}
</script>

<style scoped>
.auto-reservation-config {
  padding: 20px;
}
</style>
```

## 6. Python脚本接口规范

### 6.1 脚本目录结构

```
@reservationXuexitong/
├── main.py                 # 主执行脚本
├── config.py              # 配置管理
├── reservation_handler.py  # 预约处理逻辑
├── utils/
│   ├── __init__.py
│   ├── logger.py          # 日志工具
│   ├── http_client.py     # HTTP客户端
│   └── crypto.py          # 加密解密工具
├── requirements.txt       # 依赖包
└── README.md             # 说明文档
```

### 6.2 主执行脚本 (main.py)

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import json
import argparse
import logging
from datetime import datetime, timedelta
from reservation_handler import ReservationHandler
from utils.logger import setup_logger

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='自动预约脚本')

    parser.add_argument('--username', required=True, help='用户名')
    parser.add_argument('--password', required=True, help='密码')
    parser.add_argument('--target-date', required=True, help='目标日期 (YYYY-MM-DD)')
    parser.add_argument('--preferred-rooms', help='偏好房间ID列表 (JSON格式)')
    parser.add_argument('--seat-types', help='座位类型偏好 (逗号分隔)')
    parser.add_argument('--avoid-seats', help='避免的座位ID列表 (JSON格式)')
    parser.add_argument('--retry-count', type=int, default=3, help='重试次数')
    parser.add_argument('--retry-interval', type=int, default=5, help='重试间隔(分钟)')
    parser.add_argument('--log-level', default='INFO', help='日志级别')

    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()

    # 设置日志
    logger = setup_logger(level=args.log_level)

    try:
        # 解析参数
        preferred_rooms = json.loads(args.preferred_rooms) if args.preferred_rooms else []
        seat_types = args.seat_types.split(',') if args.seat_types else []
        avoid_seats = json.loads(args.avoid_seats) if args.avoid_seats else []

        # 创建预约处理器
        handler = ReservationHandler(
            username=args.username,
            password=args.password,
            logger=logger
        )

        # 执行预约
        result = handler.make_reservation(
            target_date=args.target_date,
            preferred_rooms=preferred_rooms,
            seat_types=seat_types,
            avoid_seats=avoid_seats,
            retry_count=args.retry_count,
            retry_interval=args.retry_interval
        )

        # 输出结果 (JSON格式，供Java程序解析)
        output = {
            'success': result['success'],
            'roomId': result.get('room_id'),
            'seatId': result.get('seat_id'),
            'message': result.get('message'),
            'details': result.get('details', {}),
            'timestamp': datetime.now().isoformat()
        }

        print(json.dumps(output, ensure_ascii=False))

        # 设置退出码
        sys.exit(0 if result['success'] else 1)

    except Exception as e:
        error_output = {
            'success': False,
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }
        print(json.dumps(error_output, ensure_ascii=False))
        logger.error(f"执行失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == '__main__':
    main()
```

### 6.3 预约处理器 (reservation_handler.py)

```python
import time
import requests
from datetime import datetime, timedelta
from utils.http_client import HttpClient

class ReservationHandler:
    """预约处理器"""

    def __init__(self, username, password, logger):
        self.username = username
        self.password = password
        self.logger = logger
        self.http_client = HttpClient(logger)
        self.session_token = None

    def make_reservation(self, target_date, preferred_rooms=None,
                        seat_types=None, avoid_seats=None,
                        retry_count=3, retry_interval=5):
        """执行预约"""

        self.logger.info(f"开始预约: 目标日期={target_date}, 用户={self.username}")

        for attempt in range(retry_count + 1):
            try:
                # 登录
                if not self.login():
                    raise Exception("登录失败")

                # 查找可用座位
                available_seats = self.find_available_seats(
                    target_date, preferred_rooms, seat_types, avoid_seats
                )

                if not available_seats:
                    raise Exception("没有找到可用座位")

                # 尝试预约第一个可用座位
                seat = available_seats[0]
                if self.reserve_seat(seat, target_date):
                    self.logger.info(f"预约成功: 房间={seat['room_id']}, 座位={seat['seat_id']}")
                    return {
                        'success': True,
                        'room_id': seat['room_id'],
                        'seat_id': seat['seat_id'],
                        'message': '预约成功',
                        'details': {
                            'attempt': attempt + 1,
                            'seat_info': seat
                        }
                    }
                else:
                    raise Exception("预约座位失败")

            except Exception as e:
                self.logger.warning(f"第{attempt + 1}次尝试失败: {e}")

                if attempt < retry_count:
                    self.logger.info(f"等待{retry_interval}分钟后重试...")
                    time.sleep(retry_interval * 60)
                else:
                    self.logger.error("所有重试都失败了")
                    return {
                        'success': False,
                        'message': str(e),
                        'details': {
                            'total_attempts': retry_count + 1
                        }
                    }

    def login(self):
        """登录到目标系统"""
        try:
            # 实现具体的登录逻辑
            login_data = {
                'username': self.username,
                'password': self.password
            }

            response = self.http_client.post('/api/login', data=login_data)

            if response.get('success'):
                self.session_token = response.get('token')
                self.logger.info("登录成功")
                return True
            else:
                self.logger.error(f"登录失败: {response.get('message')}")
                return False

        except Exception as e:
            self.logger.error(f"登录异常: {e}")
            return False

    def find_available_seats(self, target_date, preferred_rooms=None,
                           seat_types=None, avoid_seats=None):
        """查找可用座位"""
        try:
            # 构建查询参数
            params = {
                'date': target_date,
                'token': self.session_token
            }

            if preferred_rooms:
                params['room_ids'] = ','.join(map(str, preferred_rooms))

            response = self.http_client.get('/api/seats/available', params=params)

            if not response.get('success'):
                raise Exception(f"查询座位失败: {response.get('message')}")

            seats = response.get('data', [])

            # 过滤座位
            filtered_seats = self.filter_seats(seats, seat_types, avoid_seats)

            self.logger.info(f"找到{len(filtered_seats)}个可用座位")
            return filtered_seats

        except Exception as e:
            self.logger.error(f"查找座位异常: {e}")
            return []

    def filter_seats(self, seats, seat_types=None, avoid_seats=None):
        """过滤座位"""
        filtered = seats

        # 按座位类型过滤
        if seat_types:
            filtered = [s for s in filtered if s.get('type') in seat_types]

        # 排除指定座位
        if avoid_seats:
            filtered = [s for s in filtered if s.get('seat_id') not in avoid_seats]

        # 按偏好排序 (靠窗优先等)
        filtered.sort(key=lambda s: self.get_seat_priority(s))

        return filtered

    def get_seat_priority(self, seat):
        """获取座位优先级 (数字越小优先级越高)"""
        priority = 0

        # 靠窗座位优先级高
        if seat.get('type') == 'window':
            priority -= 10

        # 根据位置调整优先级
        row = seat.get('row', 0)
        col = seat.get('col', 0)

        # 中间行优先级高
        priority += abs(row - 5)

        return priority

    def reserve_seat(self, seat, target_date):
        """预约座位"""
        try:
            reservation_data = {
                'room_id': seat['room_id'],
                'seat_id': seat['seat_id'],
                'date': target_date,
                'token': self.session_token
            }

            response = self.http_client.post('/api/reservations', data=reservation_data)

            if response.get('success'):
                self.logger.info(f"座位预约成功: {seat['seat_id']}")
                return True
            else:
                self.logger.error(f"座位预约失败: {response.get('message')}")
                return False

        except Exception as e:
            self.logger.error(f"预约座位异常: {e}")
            return False
```

## 7. 部署和配置

### 7.1 应用配置

```yaml
# application.yml 新增配置
auto-reservation:
  python:
    script-path: "/opt/seatmaster/@reservationXuexitong"
    timeout: 300
    max-concurrent: 5

  scheduler:
    enabled: true
    thread-pool-size: 10

  security:
    password-encryption-key: "${AUTO_RESERVATION_ENCRYPT_KEY:default-key}"

  notification:
    enabled: true
    email:
      enabled: true
      template: "auto-reservation-result"
    webhook:
      enabled: false
      url: ""

logging:
  level:
    com.seatmaster.autoreservation: DEBUG
```

### 7.2 Python环境配置

```bash
# 安装Python依赖
cd @reservationXuexitong
pip install -r requirements.txt

# requirements.txt 内容
requests>=2.28.0
beautifulsoup4>=4.11.0
selenium>=4.5.0
cryptography>=3.4.8
python-dateutil>=2.8.2
```

## 8. 总结

这个简化的自动预约系统设计具有以下特点：

### 8.1 优势
- **架构简单**: 基于现有系统，最小化改动
- **易于维护**: 逻辑清晰，组件职责明确
- **灵活配置**: 支持多种预约策略和偏好设置
- **可靠执行**: 重试机制、日志记录、错误处理
- **用户友好**: 直观的Web界面管理配置

### 8.2 实施步骤
1. **数据库扩展** (1周): 添加必要的表和字段
2. **后端API开发** (2-3周): 实现配置管理和脚本调用
3. **前端界面开发** (2周): 创建配置管理页面
4. **Python脚本适配** (1-2周): 按照接口规范调整现有脚本
5. **测试和部署** (1周): 完整测试和生产部署

### 8.3 扩展性
- 支持多种目标系统 (不仅限于学习通)
- 可以添加更多预约策略
- 支持通知功能 (邮件、短信、webhook)
- 可以集成更多的座位选择算法

这个方案既保持了系统的简单性，又提供了完整的自动预约功能，是一个实用且可靠的解决方案。
```