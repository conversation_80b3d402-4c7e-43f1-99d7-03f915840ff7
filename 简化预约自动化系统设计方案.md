# 基于现有数据库的简化预约自动化系统设计方案

## 1. 系统概述

基于现有SeatMaster系统，**充分利用现有数据库表结构**，通过最小化改动实现自动预约功能。让Python脚本读取现有的reservations表数据，自动执行预约操作。

### 1.1 设计目标
- **零数据库结构变更**：完全基于现有表结构
- **最小化代码改动**：仅添加必要的功能模块
- **利用现有字段**：
  - `users.remainingDays` → 控制是否启用自动预约 (>0启用)
  - `reservations.reservationOpenTime` → 预约执行时间
  - `reservations.reservationType` → 预约类型(ADVANCE_ONE_DAY)
  - `reservations.status` → 预约状态控制
  - `reservations.roomId` → 目标房间
  - `reservations.seatId` → 目标座位

### 1.2 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   现有Vue前端    │    │   现有MySQL表    │    │  Python预约脚本  │
│  (预约管理界面)  │◄──►│  users/reserva  │◄──►│ (@reservationXue │
│                │    │  tions/rooms    │    │  xitong文件夹)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  现有Spring Boot │    │   现有表结构     │    │   定时任务调度   │
│   + 新增API     │    │   + 新增字段     │    │   (cron/定时器)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 2. 基于现有表的设计

### 2.1 利用现有users表
```sql
-- 现有字段利用：
-- id: 用户ID
-- username: 用户名
-- remainingDays: 控制自动预约启用状态 (>0启用，=0禁用)
-- 无需添加新字段！
```

### 2.2 扩展reservations表 (最小化添加)
```sql
-- 现有字段利用：
-- user_id: 用户ID
-- room_id: 目标房间ID
-- seat_id: 目标座位ID
-- start_time/end_time: 预约时间段
-- status: 预约状态 (新增 'AUTO_PENDING' 状态)
-- reservation_open_time: 预约执行时间 (如 "08:00:00")
-- reservation_type: 预约类型 (ADVANCE_ONE_DAY)
-- created_time: 创建时间

-- 仅需添加的字段：
ALTER TABLE reservations ADD COLUMN target_system VARCHAR(50) DEFAULT 'xuexitong' COMMENT '目标系统';
ALTER TABLE reservations ADD COLUMN xuexitong_username VARCHAR(100) COMMENT '学习通用户名';
ALTER TABLE reservations ADD COLUMN xuexitong_password_encrypted TEXT COMMENT '学习通密码(加密)';
ALTER TABLE reservations ADD COLUMN auto_reservation_config TEXT COMMENT '自动预约配置(JSON)';
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 扩展status枚举
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';
```

### 2.3 新增执行日志表 (简化版)
```sql
CREATE TABLE auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',

    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 3. 业务逻辑设计

### 3.1 自动预约流程
```
1. 用户在现有预约界面创建预约记录
   ├── 设置 reservation_open_time (如 "08:00:00")
   ├── 设置 reservation_type = "ADVANCE_ONE_DAY"
   ├── 设置 status = "AUTO_PENDING"
   ├── 填写 xuexitong_username 和 xuexitong_password_encrypted
   └── 配置 auto_reservation_config (JSON格式偏好设置)

2. 定时任务扫描 (每分钟执行一次)
   ├── 查询 users.remainingDays > 0 的用户
   ├── 查询这些用户的 status = "AUTO_PENDING" 的预约
   ├── 检查 reservation_open_time 是否到达执行时间
   └── 调用Python脚本执行预约

3. Python脚本执行
   ├── 接收数据库传入的参数
   ├── 登录学习通系统
   ├── 执行座位预约操作
   └── 返回执行结果

4. 结果处理
   ├── 成功：status = "AUTO_SUCCESS"
   ├── 失败：status = "AUTO_FAILED"
   └── 记录执行日志
```

### 3.2 数据流设计
```sql
-- 查询需要执行的自动预约
SELECT r.*, u.username, u.remainingDays
FROM reservations r
JOIN users u ON r.user_id = u.id
WHERE u.remainingDays > 0
  AND r.status = 'AUTO_PENDING'
  AND r.reservation_open_time = CURTIME()
  AND r.reservation_type = 'ADVANCE_ONE_DAY'
  AND (r.last_execution_time IS NULL
       OR DATE(r.last_execution_time) < CURDATE());
```

## 4. 后端API扩展 (基于现有Controller)

### 4.1 扩展现有ReservationController

```java
@RestController
@RequestMapping("/api/reservations")
public class ReservationController {

    @Autowired
    private ReservationService reservationService;

    @Autowired
    private AutoReservationService autoReservationService;

    // 现有方法保持不变...

    // 新增：创建自动预约
    @PostMapping("/auto")
    public ResponseEntity<String> createAutoReservation(@RequestBody AutoReservationRequest request) {
        Long userId = getCurrentUserId();

        // 检查用户是否有剩余天数
        User user = userService.getById(userId);
        if (user.getRemainingDays() <= 0) {
            return ResponseEntity.badRequest().body("剩余天数不足，无法创建自动预约");
        }

        // 创建自动预约记录
        Reservation reservation = new Reservation();
        reservation.setUserId(userId);
        reservation.setRoomId(request.getRoomId());
        reservation.setSeatId(request.getSeatId());
        reservation.setStartTime(request.getStartTime());
        reservation.setEndTime(request.getEndTime());
        reservation.setStatus("AUTO_PENDING");
        reservation.setReservationOpenTime(request.getReservationOpenTime());
        reservation.setReservationType("ADVANCE_ONE_DAY");
        reservation.setTargetSystem("xuexitong");
        reservation.setXuexitongUsername(request.getXuexitongUsername());
        reservation.setXuexitongPasswordEncrypted(
            passwordEncoder.encode(request.getXuexitongPassword()));
        reservation.setAutoReservationConfig(JsonUtils.toJson(request.getConfig()));

        reservationService.save(reservation);

        return ResponseEntity.ok("自动预约创建成功");
    }

    // 新增：获取用户的自动预约列表
    @GetMapping("/auto")
    public ResponseEntity<List<Reservation>> getUserAutoReservations() {
        Long userId = getCurrentUserId();

        List<Reservation> autoReservations = reservationService.list(
            new QueryWrapper<Reservation>()
                .eq("user_id", userId)
                .in("status", Arrays.asList("AUTO_PENDING", "AUTO_SUCCESS", "AUTO_FAILED"))
                .orderByDesc("created_time")
        );

        return ResponseEntity.ok(autoReservations);
    }

    // 新增：更新自动预约配置
    @PutMapping("/auto/{reservationId}")
    public ResponseEntity<String> updateAutoReservation(
            @PathVariable Long reservationId,
            @RequestBody AutoReservationRequest request) {

        Long userId = getCurrentUserId();

        Reservation reservation = reservationService.getOne(
            new QueryWrapper<Reservation>()
                .eq("id", reservationId)
                .eq("user_id", userId)
                .eq("status", "AUTO_PENDING")
        );

        if (reservation == null) {
            return ResponseEntity.badRequest().body("自动预约记录不存在或无法修改");
        }

        // 更新配置
        reservation.setRoomId(request.getRoomId());
        reservation.setSeatId(request.getSeatId());
        reservation.setReservationOpenTime(request.getReservationOpenTime());
        reservation.setXuexitongUsername(request.getXuexitongUsername());
        if (StringUtils.isNotBlank(request.getXuexitongPassword())) {
            reservation.setXuexitongPasswordEncrypted(
                passwordEncoder.encode(request.getXuexitongPassword()));
        }
        reservation.setAutoReservationConfig(JsonUtils.toJson(request.getConfig()));

        reservationService.updateById(reservation);

        return ResponseEntity.ok("自动预约更新成功");
    }

    // 新增：删除自动预约
    @DeleteMapping("/auto/{reservationId}")
    public ResponseEntity<String> deleteAutoReservation(@PathVariable Long reservationId) {
        Long userId = getCurrentUserId();

        Reservation reservation = reservationService.getOne(
            new QueryWrapper<Reservation>()
                .eq("id", reservationId)
                .eq("user_id", userId)
                .eq("status", "AUTO_PENDING")
        );

        if (reservation == null) {
            return ResponseEntity.badRequest().body("自动预约记录不存在");
        }

        reservationService.removeById(reservationId);
        return ResponseEntity.ok("自动预约删除成功");
    }

    // 新增：手动触发自动预约
    @PostMapping("/auto/{reservationId}/execute")
    public ResponseEntity<String> manualExecuteAutoReservation(@PathVariable Long reservationId) {
        Long userId = getCurrentUserId();

        try {
            ExecutionResult result = autoReservationService.executeReservation(reservationId, userId);

            if (result.isSuccess()) {
                return ResponseEntity.ok("预约执行成功");
            } else {
                return ResponseEntity.badRequest().body("预约执行失败: " + result.getMessage());
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("执行异常: " + e.getMessage());
        }
    }

    // 新增：获取执行日志
    @GetMapping("/auto/{reservationId}/logs")
    public ResponseEntity<List<AutoExecutionLog>> getExecutionLogs(@PathVariable Long reservationId) {
        Long userId = getCurrentUserId();

        // 验证预约记录属于当前用户
        Reservation reservation = reservationService.getOne(
            new QueryWrapper<Reservation>()
                .eq("id", reservationId)
                .eq("user_id", userId)
        );

        if (reservation == null) {
            return ResponseEntity.badRequest().body(null);
        }

        List<AutoExecutionLog> logs = autoExecutionLogService.list(
            new QueryWrapper<AutoExecutionLog>()
                .eq("reservation_id", reservationId)
                .orderByDesc("execution_time")
        );

        return ResponseEntity.ok(logs);
    }
}
```

### 4.2 新增AutoReservationService

```java
@Service
@Transactional
public class AutoReservationService {

    @Autowired
    private ReservationService reservationService;

    @Autowired
    private UserService userService;

    @Autowired
    private AutoExecutionLogService logService;

    @Autowired
    private PythonScriptExecutor pythonExecutor;

    @Value("${auto-reservation.python.script-path}")
    private String pythonScriptPath;

    /**
     * 执行自动预约
     */
    public ExecutionResult executeReservation(Long reservationId, Long userId) {
        // 获取预约记录
        Reservation reservation = reservationService.getById(reservationId);
        if (reservation == null || !reservation.getUserId().equals(userId)) {
            throw new BusinessException("预约记录不存在");
        }

        // 检查用户状态
        User user = userService.getById(userId);
        if (user.getRemainingDays() <= 0) {
            throw new BusinessException("用户剩余天数不足");
        }

        // 记录开始执行
        AutoExecutionLog log = new AutoExecutionLog();
        log.setReservationId(reservationId);
        log.setUserId(userId);
        log.setExecutionTime(LocalDateTime.now());

        long startTime = System.currentTimeMillis();

        try {
            // 调用Python脚本
            ExecutionResult result = pythonExecutor.executeReservation(reservation);

            long duration = (System.currentTimeMillis() - startTime) / 1000;

            // 更新预约状态
            if (result.isSuccess()) {
                reservation.setStatus("AUTO_SUCCESS");
                log.setExecutionStatus("SUCCESS");
            } else {
                reservation.setStatus("AUTO_FAILED");
                log.setExecutionStatus("FAILED");
            }

            reservation.setLastExecutionTime(LocalDateTime.now());
            reservation.setExecutionResult(JsonUtils.toJson(result));
            reservationService.updateById(reservation);

            // 记录日志
            log.setResultMessage(result.getMessage());
            log.setExecutionDuration((int) duration);
            logService.save(log);

            return result;

        } catch (Exception e) {
            long duration = (System.currentTimeMillis() - startTime) / 1000;

            // 更新为失败状态
            reservation.setStatus("AUTO_FAILED");
            reservation.setLastExecutionTime(LocalDateTime.now());
            reservation.setExecutionResult(JsonUtils.toJson(
                ExecutionResult.failure(e.getMessage())));
            reservationService.updateById(reservation);

            // 记录错误日志
            log.setExecutionStatus("FAILED");
            log.setResultMessage(e.getMessage());
            log.setExecutionDuration((int) duration);
            logService.save(log);

            throw new BusinessException("执行失败: " + e.getMessage());
        }
    }

    /**
     * 定时任务：扫描并执行到期的自动预约
     */
    @Scheduled(cron = "0 * * * * ?") // 每分钟执行一次
    public void scanAndExecuteAutoReservations() {
        log.info("开始扫描自动预约任务...");

        // 查询需要执行的自动预约
        List<Reservation> pendingReservations = reservationService.list(
            new QueryWrapper<Reservation>()
                .eq("status", "AUTO_PENDING")
                .eq("reservation_type", "ADVANCE_ONE_DAY")
                .eq("reservation_open_time", LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")))
                .and(wrapper -> wrapper
                    .isNull("last_execution_time")
                    .or()
                    .lt("DATE(last_execution_time)", LocalDate.now())
                )
        );

        log.info("找到 {} 个待执行的自动预约", pendingReservations.size());

        for (Reservation reservation : pendingReservations) {
            try {
                // 检查用户状态
                User user = userService.getById(reservation.getUserId());
                if (user.getRemainingDays() <= 0) {
                    log.warn("用户 {} 剩余天数不足，跳过自动预约 {}",
                        user.getUsername(), reservation.getId());
                    continue;
                }

                // 异步执行预约
                CompletableFuture.runAsync(() -> {
                    try {
                        executeReservation(reservation.getId(), reservation.getUserId());
                        log.info("自动预约执行成功: reservationId={}", reservation.getId());
                    } catch (Exception e) {
                        log.error("自动预约执行失败: reservationId={}, error={}",
                            reservation.getId(), e.getMessage());
                    }
                });

            } catch (Exception e) {
                log.error("处理自动预约异常: reservationId={}, error={}",
                    reservation.getId(), e.getMessage());
            }
        }
    }
}
```

## 5. Python脚本集成 (基于现有@reservationXuexitong)

### 5.1 脚本执行器

```java
@Component
public class PythonScriptExecutor {

    @Value("${auto-reservation.python.script-path}")
    private String scriptPath;

    @Value("${auto-reservation.python.timeout:300}")
    private int timeoutSeconds;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public ExecutionResult executeReservation(Reservation reservation) {
        long startTime = System.currentTimeMillis();

        try {
            // 构建Python脚本参数 (基于现有config.json格式)
            List<String> command = buildPythonCommand(reservation);

            // 执行Python脚本
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.directory(new File(scriptPath));
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 读取输出
            String output = readProcessOutput(process);

            // 等待执行完成
            boolean finished = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);

            if (!finished) {
                process.destroyForcibly();
                throw new RuntimeException("脚本执行超时");
            }

            int exitCode = process.exitValue();

            // 解析执行结果
            ExecutionResult result = parseExecutionResult(output, exitCode);

            return result;

        } catch (Exception e) {
            return ExecutionResult.failure(e.getMessage());
        }
    }

    private List<String> buildPythonCommand(Reservation reservation) {
        List<String> command = new ArrayList<>();
        command.add("python");
        command.add("main.py"); // 现有的主执行脚本

        // 基于现有脚本的参数格式
        command.add("--username");
        command.add(reservation.getXuexitongUsername());

        command.add("--password");
        command.add(passwordEncoder.decode(reservation.getXuexitongPasswordEncrypted()));

        // 时间参数 (基于现有脚本的time字段)
        command.add("--time");
        command.add(reservation.getStartTime() + "," + reservation.getEndTime());

        // 房间ID (基于现有脚本的roomid字段)
        command.add("--roomid");
        command.add(String.valueOf(reservation.getRoomId()));

        // 座位ID (基于现有脚本的seatid字段)
        command.add("--seatid");
        command.add(reservation.getSeatId());

        // 星期几 (基于现有脚本的daysofweek字段)
        command.add("--daysofweek");
        command.add(getCurrentDayOfWeek());

        // 额外配置 (如果有)
        if (StringUtils.isNotBlank(reservation.getAutoReservationConfig())) {
            command.add("--config");
            command.add(reservation.getAutoReservationConfig());
        }

        return command;
    }

    private String getCurrentDayOfWeek() {
        // 获取明天是星期几 (因为是提前一天预约)
        LocalDate tomorrow = LocalDate.now().plusDays(1);
        DayOfWeek dayOfWeek = tomorrow.getDayOfWeek();

        // 转换为英文格式 (与现有脚本保持一致)
        switch (dayOfWeek) {
            case MONDAY: return "Monday";
            case TUESDAY: return "Tuesday";
            case WEDNESDAY: return "Wednesday";
            case THURSDAY: return "Thursday";
            case FRIDAY: return "Friday";
            case SATURDAY: return "Saturday";
            case SUNDAY: return "Sunday";
            default: return "Monday";
        }
    }

    private String readProcessOutput(Process process) throws IOException {
        StringBuilder output = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }
        }
        return output.toString();
    }

    private ExecutionResult parseExecutionResult(String output, int exitCode) {
        // 解析Python脚本的输出结果
        try {
            if (exitCode == 0) {
                // 成功情况
                return ExecutionResult.success("预约成功", output);
            } else {
                // 失败情况
                return ExecutionResult.failure("预约失败: " + output);
            }
        } catch (Exception e) {
            return ExecutionResult.failure("解析执行结果失败: " + e.getMessage());
        }
    }
}

// 执行结果类
@Data
public class ExecutionResult {
    private boolean success;
    private String message;
    private String details;
    private LocalDateTime timestamp;

    public static ExecutionResult success(String message, String details) {
        ExecutionResult result = new ExecutionResult();
        result.setSuccess(true);
        result.setMessage(message);
        result.setDetails(details);
        result.setTimestamp(LocalDateTime.now());
        return result;
    }

    public static ExecutionResult failure(String message) {
        ExecutionResult result = new ExecutionResult();
        result.setSuccess(false);
        result.setMessage(message);
        result.setTimestamp(LocalDateTime.now());
        return result;
    }
}
```

### 5.2 Python脚本适配 (基于现有脚本)

现有的@reservationXuexitong脚本需要进行小幅调整以支持命令行参数：

```python
# main.py (在现有脚本基础上添加命令行支持)
import sys
import json
import argparse
from datetime import datetime

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='学习通自动预约脚本')

    parser.add_argument('--username', required=True, help='学习通用户名')
    parser.add_argument('--password', required=True, help='学习通密码')
    parser.add_argument('--time', required=True, help='预约时间段，格式：开始时间,结束时间')
    parser.add_argument('--roomid', required=True, help='房间ID')
    parser.add_argument('--seatid', required=True, help='座位ID')
    parser.add_argument('--daysofweek', required=True, help='星期几')
    parser.add_argument('--config', help='额外配置(JSON格式)')

    return parser.parse_args()

def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()

        # 构建配置 (兼容现有config.json格式)
        time_parts = args.time.split(',')
        config = {
            "reserve": [{
                "username": args.username,
                "password": args.password,
                "time": time_parts,
                "roomid": args.roomid,
                "seatid": [args.seatid],
                "daysofweek": [args.daysofweek]
            }]
        }

        # 如果有额外配置，合并进去
        if args.config:
            extra_config = json.loads(args.config)
            config["reserve"][0].update(extra_config)

        # 调用现有的预约逻辑
        result = execute_reservation(config)

        # 输出结果 (供Java程序解析)
        if result['success']:
            print(f"SUCCESS: {result['message']}")
            sys.exit(0)
        else:
            print(f"FAILED: {result['message']}")
            sys.exit(1)

    except Exception as e:
        print(f"ERROR: {str(e)}")
        sys.exit(1)

def execute_reservation(config):
    """执行预约逻辑 (基于现有代码)"""
    try:
        # 这里调用现有的预约逻辑
        # 保持现有代码不变，只是将config.json的内容作为参数传入

        # 示例返回 (根据实际情况调整)
        return {
            'success': True,
            'message': '预约成功',
            'details': {
                'room_id': config["reserve"][0]["roomid"],
                'seat_id': config["reserve"][0]["seatid"][0],
                'time': config["reserve"][0]["time"]
            }
        }

    except Exception as e:
        return {
            'success': False,
            'message': str(e)
        }

if __name__ == '__main__':
    main()
```

### 5.3 现有脚本保持不变

现有的@reservationXuexitong文件夹中的其他文件保持不变：
- 保持现有的登录逻辑
- 保持现有的预约逻辑
- 保持现有的重试机制
- 只需要在main.py中添加命令行参数支持
```

## 6. 前端界面扩展 (基于现有预约界面)

### 6.1 扩展现有预约界面

在现有的Reservation.vue中添加自动预约功能：

```vue
<!-- 在现有Reservation.vue中添加自动预约选项 -->
<template>
  <div class="reservation">
    <!-- 现有预约界面保持不变 -->

    <!-- 新增：自动预约选项 -->
    <el-card header="自动预约设置" v-if="showAutoReservation">
      <el-form :model="autoForm" label-width="120px">
        <el-form-item label="启用自动预约">
          <el-switch v-model="autoForm.enabled" @change="toggleAutoReservation"/>
        </el-form-item>

        <template v-if="autoForm.enabled">
          <el-form-item label="预约执行时间">
            <el-time-picker
              v-model="autoForm.reservationOpenTime"
              format="HH:mm:ss"
              placeholder="选择预约执行时间"/>
            <el-text type="info" size="small">
              系统将在此时间自动执行预约
            </el-text>
          </el-form-item>

          <el-form-item label="学习通账号">
            <el-input v-model="autoForm.xuexitongUsername" placeholder="学习通用户名"/>
          </el-form-item>

          <el-form-item label="学习通密码">
            <el-input
              v-model="autoForm.xuexitongPassword"
              type="password"
              placeholder="学习通密码"
              show-password/>
          </el-form-item>

          <el-form-item label="高级配置">
            <el-collapse>
              <el-collapse-item title="座位偏好设置" name="preferences">
                <el-form-item label="偏好座位">
                  <el-input v-model="autoForm.preferredSeats" placeholder="如：001,002,003"/>
                </el-form-item>
                <el-form-item label="避免座位">
                  <el-input v-model="autoForm.avoidSeats" placeholder="如：010,020,030"/>
                </el-form-item>
              </el-collapse-item>
            </el-collapse>
          </el-form-item>
        </template>
      </el-form>
    </el-card>

    <!-- 现有提交按钮修改 -->
    <div class="form-actions">
      <el-button type="primary" @click="submitReservation">
        {{ autoForm.enabled ? '创建自动预约' : '立即预约' }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Reservation',
  data() {
    return {
      // 现有数据保持不变

      // 新增自动预约数据
      showAutoReservation: true, // 根据用户权限控制显示
      autoForm: {
        enabled: false,
        reservationOpenTime: '',
        xuexitongUsername: '',
        xuexitongPassword: '',
        preferredSeats: '',
        avoidSeats: ''
      }
    }
  },
  methods: {
    // 现有方法保持不变

    // 新增：提交预约 (支持自动预约)
    async submitReservation() {
      try {
        if (this.autoForm.enabled) {
          // 创建自动预约
          const autoRequest = {
            roomId: this.form.roomId,
            seatId: this.form.seatId,
            startTime: this.form.startTime,
            endTime: this.form.endTime,
            reservationOpenTime: this.autoForm.reservationOpenTime,
            xuexitongUsername: this.autoForm.xuexitongUsername,
            xuexitongPassword: this.autoForm.xuexitongPassword,
            config: {
              preferredSeats: this.autoForm.preferredSeats.split(',').filter(s => s.trim()),
              avoidSeats: this.autoForm.avoidSeats.split(',').filter(s => s.trim())
            }
          }

          await reservationApi.createAutoReservation(autoRequest)
          this.$message.success('自动预约创建成功')
        } else {
          // 现有的立即预约逻辑
          await this.createReservation()
        }

        // 重置表单或跳转
        this.resetForm()

      } catch (error) {
        this.$message.error('操作失败: ' + error.message)
      }
    },

    // 新增：切换自动预约
    toggleAutoReservation(enabled) {
      if (enabled) {
        // 检查用户剩余天数
        if (this.currentUser.remainingDays <= 0) {
          this.$message.warning('剩余天数不足，无法启用自动预约')
          this.autoForm.enabled = false
          return
        }

        this.$message.info('已启用自动预约模式')
      } else {
        this.$message.info('已切换到立即预约模式')
      }
    }
  }
}
</script>
```

### 6.2 新增自动预约管理页面

```vue
<!-- AutoReservationList.vue -->
<template>
  <div class="auto-reservation-list">
    <el-card header="我的自动预约">
      <el-table :data="autoReservations" style="width: 100%">
        <el-table-column prop="roomName" label="房间" width="150"/>
        <el-table-column prop="seatId" label="座位" width="100"/>
        <el-table-column prop="startTime" label="开始时间" width="100"/>
        <el-table-column prop="endTime" label="结束时间" width="100"/>
        <el-table-column prop="reservationOpenTime" label="执行时间" width="100"/>
        <el-table-column label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastExecutionTime" label="最后执行" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.lastExecutionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button
              size="small"
              @click="editAutoReservation(scope.row)"
              :disabled="scope.row.status !== 'AUTO_PENDING'">
              编辑
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="manualExecute(scope.row)"
              :disabled="scope.row.status !== 'AUTO_PENDING'">
              手动执行
            </el-button>
            <el-button
              size="small"
              @click="viewLogs(scope.row)">
              日志
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteAutoReservation(scope.row)"
              :disabled="scope.row.status !== 'AUTO_PENDING'">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 执行日志对话框 -->
    <el-dialog title="执行日志" v-model="logDialogVisible" width="800px">
      <el-table :data="logs" style="width: 100%">
        <el-table-column prop="executionTime" label="执行时间" width="150">
          <template #default="scope">
            {{ formatTime(scope.row.executionTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getLogStatusType(scope.row.executionStatus)">
              {{ scope.row.executionStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resultMessage" label="结果信息" show-overflow-tooltip/>
        <el-table-column prop="executionDuration" label="耗时(秒)" width="80"/>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'AutoReservationList',
  data() {
    return {
      autoReservations: [],
      logs: [],
      logDialogVisible: false,
      currentReservationId: null
    }
  },
  mounted() {
    this.loadAutoReservations()
  },
  methods: {
    async loadAutoReservations() {
      try {
        const response = await reservationApi.getUserAutoReservations()
        this.autoReservations = response.data
      } catch (error) {
        this.$message.error('加载自动预约列表失败')
      }
    },

    async manualExecute(reservation) {
      try {
        this.$message.info('开始执行预约...')
        await reservationApi.manualExecuteAutoReservation(reservation.id)
        this.$message.success('预约执行成功')
        this.loadAutoReservations()
      } catch (error) {
        this.$message.error('执行失败: ' + error.message)
      }
    },

    async viewLogs(reservation) {
      this.currentReservationId = reservation.id
      this.logDialogVisible = true

      try {
        const response = await reservationApi.getExecutionLogs(reservation.id)
        this.logs = response.data
      } catch (error) {
        this.$message.error('加载日志失败')
      }
    },

    async deleteAutoReservation(reservation) {
      try {
        await this.$confirm('确定要删除这个自动预约吗？', '确认删除')
        await reservationApi.deleteAutoReservation(reservation.id)
        this.$message.success('删除成功')
        this.loadAutoReservations()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    getStatusType(status) {
      const typeMap = {
        'AUTO_PENDING': 'warning',
        'AUTO_SUCCESS': 'success',
        'AUTO_FAILED': 'danger'
      }
      return typeMap[status] || 'info'
    },

    getStatusText(status) {
      const textMap = {
        'AUTO_PENDING': '等待执行',
        'AUTO_SUCCESS': '执行成功',
        'AUTO_FAILED': '执行失败'
      }
      return textMap[status] || status
    },

    getLogStatusType(status) {
      const typeMap = {
        'SUCCESS': 'success',
        'FAILED': 'danger',
        'TIMEOUT': 'warning'
      }
      return typeMap[status] || 'info'
    },

    formatTime(time) {
      return time ? new Date(time).toLocaleString() : '-'
    }
  }
}
</script>
```

## 7. 部署和配置

### 7.1 数据库迁移脚本

```sql
-- 基于现有表的最小化扩展
USE seat_reservation;

-- 1. 扩展reservations表
ALTER TABLE reservations ADD COLUMN target_system VARCHAR(50) DEFAULT 'xuexitong' COMMENT '目标系统';
ALTER TABLE reservations ADD COLUMN xuexitong_username VARCHAR(100) COMMENT '学习通用户名';
ALTER TABLE reservations ADD COLUMN xuexitong_password_encrypted TEXT COMMENT '学习通密码(加密)';
ALTER TABLE reservations ADD COLUMN auto_reservation_config TEXT COMMENT '自动预约配置(JSON)';
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 2. 扩展status枚举
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

-- 3. 创建执行日志表
CREATE TABLE auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',

    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 4. 创建索引优化查询
CREATE INDEX idx_reservations_auto_pending ON reservations(status, reservation_open_time, reservation_type);
CREATE INDEX idx_users_remaining_days ON users(remaining_days);

COMMIT;
```

### 7.2 应用配置

```yaml
# application.yml 新增配置
auto-reservation:
  python:
    script-path: "/opt/seatmaster/@reservationXuexitong"
    timeout: 300
    max-concurrent: 3

  scheduler:
    enabled: true
    cron: "0 * * * * ?" # 每分钟执行一次

  security:
    password-encryption-key: "${AUTO_RESERVATION_ENCRYPT_KEY:seatmaster-2024}"

logging:
  level:
    com.seatmaster.autoreservation: DEBUG
```

### 7.3 Python环境配置

```bash
# 确保Python脚本可执行
cd @reservationXuexitong
chmod +x main.py

# 测试脚本是否正常工作
python main.py --username test --password test --time "08:00,10:00" --roomid 1769 --seatid 001 --daysofweek Monday
```

## 8. 实施步骤

### 8.1 第一阶段：数据库扩展 (1天)
1. 执行数据库迁移脚本
2. 验证表结构正确性
3. 测试现有功能不受影响

### 8.2 第二阶段：后端开发 (3-4天)
1. 扩展Reservation实体类
2. 创建AutoExecutionLog实体类
3. 扩展ReservationController API
4. 实现AutoReservationService
5. 实现PythonScriptExecutor
6. 添加定时任务

### 8.3 第三阶段：前端开发 (2-3天)
1. 扩展现有预约界面，添加自动预约选项
2. 创建自动预约管理页面
3. 添加执行日志查看功能
4. 测试前后端集成

### 8.4 第四阶段：Python脚本适配 (1-2天)
1. 修改现有Python脚本支持命令行参数
2. 测试脚本与Java程序的集成
3. 验证预约功能正常工作

### 8.5 第五阶段：测试部署 (1天)
1. 完整功能测试
2. 性能测试
3. 生产环境部署

## 9. 总结

### 9.1 方案优势
- **最小化改动**：基于现有表结构，仅添加必要字段
- **零学习成本**：用户在现有界面中即可使用自动预约
- **完全兼容**：不影响现有功能，可以平滑升级
- **简单可靠**：避免复杂架构，降低出错风险
- **易于维护**：代码结构清晰，便于后续维护

### 9.2 核心特性
1. **智能调度**：基于`users.remainingDays`和`reservations.reservationOpenTime`自动执行
2. **状态管理**：通过`reservations.status`字段完整跟踪预约状态
3. **安全可靠**：密码加密存储，执行日志完整记录
4. **用户友好**：在现有预约界面中无缝集成自动预约功能

### 9.3 预期效果
- 用户可以在现有预约界面中轻松启用自动预约
- 系统根据用户剩余天数自动控制预约执行
- 完整的执行日志帮助用户了解预约结果
- 管理员可以通过现有管理界面监控自动预约状态

这个方案完全基于您现有的数据库结构，最大化利用现有字段，实现了简单可靠的自动预约功能！

## 7. 部署和配置

### 7.1 应用配置

```yaml
# application.yml 新增配置
auto-reservation:
  python:
    script-path: "/opt/seatmaster/@reservationXuexitong"
    timeout: 300
    max-concurrent: 5

  scheduler:
    enabled: true
    thread-pool-size: 10

  security:
    password-encryption-key: "${AUTO_RESERVATION_ENCRYPT_KEY:default-key}"

  notification:
    enabled: true
    email:
      enabled: true
      template: "auto-reservation-result"
    webhook:
      enabled: false
      url: ""

logging:
  level:
    com.seatmaster.autoreservation: DEBUG
```

### 7.2 Python环境配置

```bash
# 安装Python依赖
cd @reservationXuexitong
pip install -r requirements.txt

# requirements.txt 内容
requests>=2.28.0
beautifulsoup4>=4.11.0
selenium>=4.5.0
cryptography>=3.4.8
python-dateutil>=2.8.2
```

## 8. 总结

这个简化的自动预约系统设计具有以下特点：

### 8.1 优势
- **架构简单**: 基于现有系统，最小化改动
- **易于维护**: 逻辑清晰，组件职责明确
- **灵活配置**: 支持多种预约策略和偏好设置
- **可靠执行**: 重试机制、日志记录、错误处理
- **用户友好**: 直观的Web界面管理配置

### 8.2 实施步骤
1. **数据库扩展** (1周): 添加必要的表和字段
2. **后端API开发** (2-3周): 实现配置管理和脚本调用
3. **前端界面开发** (2周): 创建配置管理页面
4. **Python脚本适配** (1-2周): 按照接口规范调整现有脚本
5. **测试和部署** (1周): 完整测试和生产部署

### 8.3 扩展性
- 支持多种目标系统 (不仅限于学习通)
- 可以添加更多预约策略
- 支持通知功能 (邮件、短信、webhook)
- 可以集成更多的座位选择算法

这个方案既保持了系统的简单性，又提供了完整的自动预约功能，是一个实用且可靠的解决方案。
```