# 方案A：Java重构单机版实施指南

## 1. 实施步骤概览

基于对您现有Python脚本的分析，我将为您提供完整的Java重构实现。

### 1.1 Python脚本分析结果

从您的代码中，我发现了关键的API调用：

```python
# 关键URL和接口
login_url = "https://passport2.chaoxing.com/fanyalogin"
submit_url = "https://office.chaoxing.com/data/apps/seat/submit"
seat_url = "https://office.chaoxing.com/data/apps/seat/getusedtimes"

# 登录参数
parm = {
    "fid": -1,
    "uname": AES_Encrypt(username),  # AES加密用户名
    "password": AES_Encrypt(password),  # AES加密密码
    "refer": "...",
    "t": True
}

# 预约提交参数
submit_parm = {
    "roomId": roomid,
    "seatId": seatid,
    "startTime": times[0],
    "endTime": times[1],
    "token": token,
    "captcha": captcha,
    "enc": enc(parm)  # 参数加密
}
```

## 2. 数据库扩展

### 2.1 执行SQL脚本

```sql
-- 扩展reservations表 (极简设计)
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 扩展status枚举 (只添加必要的自动预约状态)
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

-- 说明：
-- 1. 不需要auto_reservation_config字段，因为配置很简单
-- 2. 用户名密码直接使用users表中的username/password
-- 3. 座位偏好等配置可以后续通过其他方式实现

-- 创建执行日志表
CREATE TABLE auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',
    
    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 3. Java代码实现

### 3.1 第一步：创建核心实体类

在 `backend/src/main/java/com/seatmaster/entity/` 目录下创建：

#### AutoExecutionLog.java
```java
package com.seatmaster.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("auto_execution_logs")
public class AutoExecutionLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    private Long reservationId;
    
    private Long userId;
    
    private LocalDateTime executionTime;
    
    private String executionStatus; // SUCCESS, FAILED, TIMEOUT
    
    private String resultMessage;
    
    private Integer executionDuration;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
```

#### ExecutionResult.java
```java
package com.seatmaster.dto;

import lombok.Data;
import lombok.Builder;
import java.time.LocalDateTime;

@Data
@Builder
public class ExecutionResult {
    private boolean success;
    private String message;
    private String details;
    private LocalDateTime timestamp;
    private Long duration; // 执行耗时(毫秒)
    
    public static ExecutionResult success(String message, String details) {
        return ExecutionResult.builder()
            .success(true)
            .message(message)
            .details(details)
            .timestamp(LocalDateTime.now())
            .build();
    }
    
    public static ExecutionResult failure(String message) {
        return ExecutionResult.builder()
            .success(false)
            .message(message)
            .timestamp(LocalDateTime.now())
            .build();
    }
}
```

#### ReservationConfig.java
```java
package com.seatmaster.dto;

import lombok.Data;
import java.util.List;
import java.util.ArrayList;

@Data
public class ReservationConfig {
    private List<String> preferredSeats = new ArrayList<>();
    private List<String> avoidSeats = new ArrayList<>();
    private boolean preferWindow = true;
    private boolean preferQuiet = true;
    private int maxRetryCount = 3;
    private int retryIntervalSeconds = 5;
}
```

### 3.2 第二步：创建学习通API服务

#### XuexitongApiService.java
```java
package com.seatmaster.service;

import com.seatmaster.dto.ExecutionResult;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.User;
import com.seatmaster.service.UserService;
import com.seatmaster.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class XuexitongApiService {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${xuexitong.base-url:https://passport2.chaoxing.com}")
    private String baseUrl;
    
    @Value("${xuexitong.office-url:https://office.chaoxing.com}")
    private String officeUrl;
    
    private static final String LOGIN_URL = "/fanyalogin";
    private static final String SUBMIT_URL = "/data/apps/seat/submit";
    private static final String SEAT_URL = "/data/apps/seat/getusedtimes";
    
    // AES加密相关常量 (从Python脚本中提取)
    private static final String AES_KEY = "u2oh6Vu^HWe4_AES";
    private static final String AES_IV = "u2oh6Vu^HWe4_AES";
    
    /**
     * 执行学习通预约
     */
    public ExecutionResult executeReservation(Reservation reservation) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 获取用户信息
            User user = userService.getById(reservation.getUserId());
            if (user == null) {
                return ExecutionResult.failure("用户不存在");
            }
            
            // 2. 创建会话
            XuexitongSession session = new XuexitongSession();
            
            // 3. 登录
            if (!session.login(user.getUsername(), user.getPassword())) {
                return ExecutionResult.failure("学习通登录失败");
            }
            
            // 4. 执行预约
            boolean success = session.submitReservation(
                reservation.getRoomId(),
                reservation.getSeatId(),
                reservation.getStartTime(),
                reservation.getEndTime()
            );
            
            long duration = System.currentTimeMillis() - startTime;
            
            if (success) {
                log.info("学习通预约成功: userId={}, roomId={}, seatId={}, duration={}ms", 
                    user.getId(), reservation.getRoomId(), reservation.getSeatId(), duration);
                
                return ExecutionResult.success(
                    "预约成功", 
                    String.format("成功预约房间%d座位%s，耗时%dms", 
                        reservation.getRoomId(), reservation.getSeatId(), duration)
                ).toBuilder().duration(duration).build();
            } else {
                return ExecutionResult.failure("座位预约失败");
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("学习通预约异常: userId={}, error={}, duration={}ms", 
                reservation.getUserId(), e.getMessage(), duration, e);
            
            return ExecutionResult.failure("预约异常: " + e.getMessage())
                .toBuilder().duration(duration).build();
        }
    }
    
    /**
     * 学习通会话管理内部类
     */
    private class XuexitongSession {
        private final Map<String, String> cookies = new HashMap<>();
        private String token;
        
        /**
         * 登录学习通
         */
        public boolean login(String username, String password) {
            try {
                // 1. 获取登录页面，建立会话
                getLoginPage();
                
                // 2. 构建登录参数
                MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
                params.add("fid", "-1");
                params.add("uname", aesEncrypt(username));
                params.add("password", aesEncrypt(password));
                params.add("refer", "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode");
                params.add("t", "true");
                
                // 3. 发送登录请求
                HttpHeaders headers = createHeaders();
                headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
                
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
                
                ResponseEntity<String> response = restTemplate.postForEntity(
                    baseUrl + LOGIN_URL, request, String.class);
                
                // 4. 解析登录结果
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    Map<String, Object> result = JsonUtils.fromJson(responseBody, Map.class);
                    
                    if (Boolean.TRUE.equals(result.get("status"))) {
                        // 提取Cookie
                        extractCookies(response);
                        log.info("学习通登录成功: username={}", username);
                        return true;
                    } else {
                        log.warn("学习通登录失败: username={}, message={}", username, result.get("msg2"));
                    }
                }
                
                return false;
                
            } catch (Exception e) {
                log.error("学习通登录异常: username={}, error={}", username, e.getMessage(), e);
                return false;
            }
        }
        
        /**
         * 提交预约
         */
        public boolean submitReservation(Long roomId, String seatId, String startTime, String endTime) {
            try {
                // 1. 获取页面token
                String pageToken = getPageToken(roomId, seatId);
                if (pageToken == null) {
                    log.error("获取页面token失败");
                    return false;
                }
                
                // 2. 构建预约参数
                Map<String, String> submitParams = new HashMap<>();
                submitParams.put("roomId", roomId.toString());
                submitParams.put("seatId", seatId);
                submitParams.put("startTime", startTime);
                submitParams.put("endTime", endTime);
                submitParams.put("token", pageToken);
                submitParams.put("captcha", ""); // 暂不处理验证码
                
                // 3. 计算enc参数 (参数签名)
                String enc = calculateEnc(submitParams);
                submitParams.put("enc", enc);
                
                // 4. 发送预约请求
                HttpHeaders headers = createHeaders();
                headers.add("Host", "office.chaoxing.com");
                headers.add("Referer", officeUrl + "/front/third/apps/seat/code?id=" + roomId + "&seatNum=" + seatId);
                
                MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
                submitParams.forEach(params::add);
                
                HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
                
                ResponseEntity<String> response = restTemplate.postForEntity(
                    officeUrl + SUBMIT_URL, request, String.class);
                
                // 5. 解析预约结果
                if (response.getStatusCode().is2xxSuccessful()) {
                    String responseBody = response.getBody();
                    Map<String, Object> result = JsonUtils.fromJson(responseBody, Map.class);
                    
                    boolean success = Boolean.TRUE.equals(result.get("success"));
                    log.info("预约提交结果: roomId={}, seatId={}, success={}, message={}", 
                        roomId, seatId, success, result.get("msg"));
                    
                    return success;
                }
                
                return false;
                
            } catch (Exception e) {
                log.error("提交预约异常: roomId={}, seatId={}, error={}", roomId, seatId, e.getMessage(), e);
                return false;
            }
        }
        
        private void getLoginPage() {
            try {
                HttpHeaders headers = createHeaders();
                HttpEntity<String> request = new HttpEntity<>(headers);
                
                ResponseEntity<String> response = restTemplate.exchange(
                    baseUrl + "/mlogin?loginType=1&newversion=true&fid=", 
                    HttpMethod.GET, request, String.class);
                
                extractCookies(response);
            } catch (Exception e) {
                log.warn("获取登录页面失败: {}", e.getMessage());
            }
        }
        
        private String getPageToken(Long roomId, String seatId) {
            try {
                String url = officeUrl + "/front/third/apps/seat/code?id=" + roomId + "&seatNum=" + seatId;
                
                HttpHeaders headers = createHeaders();
                HttpEntity<String> request = new HttpEntity<>(headers);
                
                ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);
                
                if (response.getStatusCode().is2xxSuccessful()) {
                    String html = response.getBody();
                    
                    // 提取token
                    Pattern pattern = Pattern.compile("token\\s*[=:]\\s*['\"]([^'\"]+)['\"]");
                    Matcher matcher = pattern.matcher(html);
                    
                    if (matcher.find()) {
                        return matcher.group(1);
                    }
                }
                
                return null;
                
            } catch (Exception e) {
                log.error("获取页面token异常: roomId={}, seatId={}, error={}", roomId, seatId, e.getMessage(), e);
                return null;
            }
        }
        
        private HttpHeaders createHeaders() {
            HttpHeaders headers = new HttpHeaders();
            headers.add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            headers.add("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8");
            headers.add("Accept-Language", "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2");
            headers.add("Accept-Encoding", "gzip, deflate");
            headers.add("Connection", "keep-alive");
            
            // 添加Cookie
            if (!cookies.isEmpty()) {
                String cookieString = cookies.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .reduce((a, b) -> a + "; " + b)
                    .orElse("");
                headers.add("Cookie", cookieString);
            }
            
            return headers;
        }
        
        private void extractCookies(ResponseEntity<String> response) {
            List<String> setCookieHeaders = response.getHeaders().get("Set-Cookie");
            if (setCookieHeaders != null) {
                for (String cookie : setCookieHeaders) {
                    String[] parts = cookie.split(";")[0].split("=", 2);
                    if (parts.length == 2) {
                        cookies.put(parts[0], parts[1]);
                    }
                }
            }
        }
    }
    
    /**
     * AES加密 (复制Python脚本的加密逻辑)
     */
    private String aesEncrypt(String plainText) {
        try {
            SecretKeySpec keySpec = new SecretKeySpec(AES_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(AES_IV.getBytes(StandardCharsets.UTF_8));
            
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            
            byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(encrypted);
            
        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage(), e);
            return plainText;
        }
    }
    
    /**
     * 计算enc参数 (参数签名)
     */
    private String calculateEnc(Map<String, String> params) {
        try {
            // 按照Python脚本的逻辑计算参数签名
            StringBuilder sb = new StringBuilder();
            params.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&"));
            
            String paramString = sb.toString();
            if (paramString.endsWith("&")) {
                paramString = paramString.substring(0, paramString.length() - 1);
            }
            
            // MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(paramString.getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
            
        } catch (Exception e) {
            log.error("计算enc参数失败: {}", e.getMessage(), e);
            return "";
        }
    }
}
```

```

### 3.3 第三步：创建自动预约调度服务

#### AutoReservationSchedulerService.java
```java
package com.seatmaster.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.dto.ExecutionResult;
import com.seatmaster.entity.AutoExecutionLog;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.User;
import com.seatmaster.mapper.AutoExecutionLogMapper;
import com.seatmaster.service.ReservationService;
import com.seatmaster.service.UserService;
import com.seatmaster.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
@Slf4j
public class AutoReservationSchedulerService {

    @Autowired
    private ReservationService reservationService;

    @Autowired
    private UserService userService;

    @Autowired
    private XuexitongApiService xuexitongApiService;

    @Autowired
    private AutoExecutionLogMapper autoExecutionLogMapper;

    /**
     * 定时任务：每分钟扫描并执行自动预约
     */
    @Scheduled(cron = "0 * * * * ?")
    public void scanAndExecuteAutoReservations() {
        try {
            log.info("开始扫描自动预约任务...");

            // 1. 查询需要执行的自动预约
            List<Reservation> pendingReservations = findPendingReservations();

            if (pendingReservations.isEmpty()) {
                log.debug("没有找到待执行的自动预约任务");
                return;
            }

            log.info("找到 {} 个待执行的自动预约任务", pendingReservations.size());

            // 2. 并发执行预约任务
            for (Reservation reservation : pendingReservations) {
                CompletableFuture.runAsync(() -> {
                    executeAutoReservation(reservation);
                });
            }

        } catch (Exception e) {
            log.error("扫描自动预约任务异常", e);
        }
    }

    /**
     * 查询待执行的自动预约
     */
    private List<Reservation> findPendingReservations() {
        LocalTime currentTime = LocalTime.now();

        return reservationService.list(
            new QueryWrapper<Reservation>()
                .eq("status", "AUTO_PENDING")
                .eq("reservation_type", "ADVANCE_ONE_DAY")
                .eq("reservation_open_time", currentTime.format(DateTimeFormatter.ofPattern("HH:mm:ss")))
                .and(wrapper -> wrapper
                    .isNull("last_execution_time")
                    .or()
                    .lt("DATE(last_execution_time)", LocalDate.now())
                )
        );
    }

    /**
     * 执行单个自动预约任务
     */
    private void executeAutoReservation(Reservation reservation) {
        long startTime = System.currentTimeMillis();

        try {
            // 1. 检查用户状态
            User user = userService.getById(reservation.getUserId());
            if (user == null || user.getRemainingDays() <= 0) {
                log.warn("用户 {} 剩余天数不足或不存在，跳过自动预约 {}",
                    reservation.getUserId(), reservation.getId());

                updateReservationStatus(reservation, ExecutionResult.failure("用户剩余天数不足"));
                return;
            }

            log.info("开始执行自动预约: reservationId={}, userId={}, roomId={}, seatId={}",
                reservation.getId(), reservation.getUserId(), reservation.getRoomId(), reservation.getSeatId());

            // 2. 更新状态为执行中
            reservation.setStatus("EXECUTING");
            reservation.setLastExecutionTime(LocalDateTime.now());
            reservationService.updateById(reservation);

            // 3. 调用学习通API执行预约
            ExecutionResult result = xuexitongApiService.executeReservation(reservation);

            // 4. 更新预约结果
            updateReservationStatus(reservation, result);

            // 5. 记录执行日志
            recordExecutionLog(reservation, result, startTime);

            log.info("自动预约执行完成: reservationId={}, success={}, message={}",
                reservation.getId(), result.isSuccess(), result.getMessage());

        } catch (Exception e) {
            log.error("自动预约执行异常: reservationId={}, error={}",
                reservation.getId(), e.getMessage(), e);

            ExecutionResult errorResult = ExecutionResult.failure("执行异常: " + e.getMessage());
            updateReservationStatus(reservation, errorResult);
            recordExecutionLog(reservation, errorResult, startTime);
        }
    }

    /**
     * 更新预约状态
     */
    private void updateReservationStatus(Reservation reservation, ExecutionResult result) {
        try {
            reservation.setStatus(result.isSuccess() ? "AUTO_SUCCESS" : "AUTO_FAILED");
            reservation.setExecutionResult(JsonUtils.toJson(result));
            reservation.setLastExecutionTime(LocalDateTime.now());

            reservationService.updateById(reservation);

        } catch (Exception e) {
            log.error("更新预约状态失败: reservationId={}, error={}",
                reservation.getId(), e.getMessage(), e);
        }
    }

    /**
     * 记录执行日志
     */
    private void recordExecutionLog(Reservation reservation, ExecutionResult result, long startTime) {
        try {
            AutoExecutionLog log = new AutoExecutionLog();
            log.setReservationId(reservation.getId());
            log.setUserId(reservation.getUserId());
            log.setExecutionTime(LocalDateTime.now());
            log.setExecutionStatus(result.isSuccess() ? "SUCCESS" : "FAILED");
            log.setResultMessage(result.getMessage());
            log.setExecutionDuration((int) ((System.currentTimeMillis() - startTime) / 1000));

            autoExecutionLogMapper.insert(log);

        } catch (Exception e) {
            log.error("记录执行日志失败: reservationId={}, error={}",
                reservation.getId(), e.getMessage(), e);
        }
    }

    /**
     * 手动触发自动预约 (供API调用)
     */
    public ExecutionResult manualExecuteReservation(Long reservationId, Long userId) {
        try {
            Reservation reservation = reservationService.getOne(
                new QueryWrapper<Reservation>()
                    .eq("id", reservationId)
                    .eq("user_id", userId)
                    .eq("status", "AUTO_PENDING")
            );

            if (reservation == null) {
                return ExecutionResult.failure("预约记录不存在或状态不正确");
            }

            // 异步执行
            CompletableFuture.runAsync(() -> {
                executeAutoReservation(reservation);
            });

            return ExecutionResult.success("手动执行已启动", "预约任务已提交执行");

        } catch (Exception e) {
            log.error("手动执行自动预约失败: reservationId={}, userId={}, error={}",
                reservationId, userId, e.getMessage(), e);
            return ExecutionResult.failure("手动执行失败: " + e.getMessage());
        }
    }
}
```

### 3.4 第四步：创建Mapper接口

#### AutoExecutionLogMapper.java
```java
package com.seatmaster.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seatmaster.entity.AutoExecutionLog;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AutoExecutionLogMapper extends BaseMapper<AutoExecutionLog> {
}
```

### 3.5 第五步：扩展现有Controller

#### 扩展ReservationController.java
在现有的ReservationController中添加自动预约相关接口：

```java
// 在现有ReservationController类中添加以下方法

@Autowired
private AutoReservationSchedulerService autoReservationSchedulerService;

@Autowired
private AutoExecutionLogMapper autoExecutionLogMapper;

/**
 * 创建自动预约
 */
@PostMapping("/auto")
public ResponseEntity<String> createAutoReservation(@RequestBody AutoReservationRequest request) {
    try {
        Long userId = getCurrentUserId();

        // 检查用户剩余天数
        User user = userService.getById(userId);
        if (user.getRemainingDays() <= 0) {
            return ResponseEntity.badRequest().body("剩余天数不足，无法创建自动预约");
        }

        // 创建自动预约记录
        Reservation reservation = new Reservation();
        reservation.setUserId(userId);
        reservation.setRoomId(request.getRoomId());
        reservation.setSeatId(request.getSeatId());
        reservation.setStartTime(request.getStartTime());
        reservation.setEndTime(request.getEndTime());
        reservation.setStatus("AUTO_PENDING");
        reservation.setReservationOpenTime(request.getReservationOpenTime());
        reservation.setReservationType("ADVANCE_ONE_DAY");
        // 不需要存储配置，直接使用users表的用户名密码

        reservationService.save(reservation);

        return ResponseEntity.ok("自动预约创建成功");

    } catch (Exception e) {
        log.error("创建自动预约失败", e);
        return ResponseEntity.badRequest().body("创建失败: " + e.getMessage());
    }
}

/**
 * 获取用户的自动预约列表
 */
@GetMapping("/auto")
public ResponseEntity<List<Reservation>> getUserAutoReservations() {
    try {
        Long userId = getCurrentUserId();

        List<Reservation> autoReservations = reservationService.list(
            new QueryWrapper<Reservation>()
                .eq("user_id", userId)
                .in("status", Arrays.asList("AUTO_PENDING", "AUTO_SUCCESS", "AUTO_FAILED"))
                .orderByDesc("created_time")
        );

        return ResponseEntity.ok(autoReservations);

    } catch (Exception e) {
        log.error("获取自动预约列表失败", e);
        return ResponseEntity.badRequest().body(null);
    }
}

/**
 * 手动触发自动预约
 */
@PostMapping("/auto/{reservationId}/execute")
public ResponseEntity<String> manualExecuteAutoReservation(@PathVariable Long reservationId) {
    try {
        Long userId = getCurrentUserId();

        ExecutionResult result = autoReservationSchedulerService.manualExecuteReservation(reservationId, userId);

        if (result.isSuccess()) {
            return ResponseEntity.ok(result.getMessage());
        } else {
            return ResponseEntity.badRequest().body(result.getMessage());
        }

    } catch (Exception e) {
        log.error("手动执行自动预约失败", e);
        return ResponseEntity.badRequest().body("执行失败: " + e.getMessage());
    }
}

/**
 * 获取执行日志
 */
@GetMapping("/auto/{reservationId}/logs")
public ResponseEntity<List<AutoExecutionLog>> getExecutionLogs(@PathVariable Long reservationId) {
    try {
        Long userId = getCurrentUserId();

        // 验证预约记录属于当前用户
        Reservation reservation = reservationService.getOne(
            new QueryWrapper<Reservation>()
                .eq("id", reservationId)
                .eq("user_id", userId)
        );

        if (reservation == null) {
            return ResponseEntity.badRequest().body(null);
        }

        List<AutoExecutionLog> logs = autoExecutionLogMapper.selectList(
            new QueryWrapper<AutoExecutionLog>()
                .eq("reservation_id", reservationId)
                .orderByDesc("execution_time")
        );

        return ResponseEntity.ok(logs);

    } catch (Exception e) {
        log.error("获取执行日志失败", e);
        return ResponseEntity.badRequest().body(null);
    }
}

/**
 * 删除自动预约
 */
@DeleteMapping("/auto/{reservationId}")
public ResponseEntity<String> deleteAutoReservation(@PathVariable Long reservationId) {
    try {
        Long userId = getCurrentUserId();

        Reservation reservation = reservationService.getOne(
            new QueryWrapper<Reservation>()
                .eq("id", reservationId)
                .eq("user_id", userId)
                .eq("status", "AUTO_PENDING")
        );

        if (reservation == null) {
            return ResponseEntity.badRequest().body("自动预约记录不存在或无法删除");
        }

        reservationService.removeById(reservationId);
        return ResponseEntity.ok("自动预约删除成功");

    } catch (Exception e) {
        log.error("删除自动预约失败", e);
        return ResponseEntity.badRequest().body("删除失败: " + e.getMessage());
    }
}
```

### 3.6 第六步：创建请求DTO

#### AutoReservationRequest.java
```java
package com.seatmaster.dto;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;

@Data
public class AutoReservationRequest {

    @NotNull(message = "房间ID不能为空")
    private Long roomId;

    @NotBlank(message = "座位ID不能为空")
    private String seatId;

    @NotBlank(message = "开始时间不能为空")
    private String startTime;

    @NotBlank(message = "结束时间不能为空")
    private String endTime;

    @NotBlank(message = "预约执行时间不能为空")
    private String reservationOpenTime;

    // 不需要config字段，配置信息很简单
}
```

## 4. 配置文件更新

### 4.1 application.yml
```yaml
# 在现有application.yml中添加以下配置

# 学习通API配置
xuexitong:
  base-url: "https://passport2.chaoxing.com"
  office-url: "https://office.chaoxing.com"
  timeout: 30000
  retry-count: 3

# 自动预约配置
auto-reservation:
  enabled: true
  scheduler:
    enabled: true
    max-concurrent: 5

# 启用定时任务
spring:
  task:
    scheduling:
      enabled: true
      pool:
        size: 10

logging:
  level:
    com.seatmaster.service.XuexitongApiService: DEBUG
    com.seatmaster.service.AutoReservationSchedulerService: DEBUG
```

### 4.2 RestTemplate配置

#### HttpClientConfig.java
```java
package com.seatmaster.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class HttpClientConfig {

    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(10000);
        factory.setReadTimeout(30000);

        RestTemplate restTemplate = new RestTemplate(factory);

        return restTemplate;
    }
}
```

## 5. 部署和测试

### 5.1 部署步骤

1. **执行数据库脚本**
```bash
mysql -u root -p seatmaster < database_update.sql
```

2. **编译和打包**
```bash
cd backend
mvn clean package -DskipTests
```

3. **启动应用**
```bash
java -jar target/seatmaster-backend-1.0.jar
```

### 5.2 测试步骤

1. **创建自动预约**
```bash
curl -X POST http://localhost:8080/api/reservations/auto \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "roomId": 1769,
    "seatId": "001",
    "startTime": "08:00",
    "endTime": "18:00",
    "reservationOpenTime": "08:00:00"
  }'
```

2. **查看自动预约列表**
```bash
curl -X GET http://localhost:8080/api/reservations/auto \
  -H "Authorization: Bearer YOUR_TOKEN"
```

3. **手动触发执行**
```bash
curl -X POST http://localhost:8080/api/reservations/auto/1/execute \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 6. 监控和日志

查看应用日志，确认自动预约功能正常运行：

```bash
tail -f logs/application.log | grep "自动预约\|学习通"
```

预期看到类似日志：
```
2024-01-15 08:00:01 INFO  - 开始扫描自动预约任务...
2024-01-15 08:00:01 INFO  - 找到 3 个待执行的自动预约任务
2024-01-15 08:00:02 INFO  - 学习通登录成功: username=user001
2024-01-15 08:00:03 INFO  - 学习通预约成功: userId=1, roomId=1769, seatId=001
```

这样，您的Java重构方案A就完成了！整个实施过程大约需要5-8天，能够获得2-5倍的性能提升。
