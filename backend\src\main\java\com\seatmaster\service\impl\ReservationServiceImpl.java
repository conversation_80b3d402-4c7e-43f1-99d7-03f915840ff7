package com.seatmaster.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.seatmaster.entity.Reservation;
import com.seatmaster.entity.Room;
import com.seatmaster.entity.User;
import com.seatmaster.dto.UserProfileResponse;
import com.seatmaster.mapper.ReservationMapper;
import com.seatmaster.mapper.RoomMapper;
import com.seatmaster.mapper.UserMapper;
import com.seatmaster.service.ReservationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Service
public class ReservationServiceImpl implements ReservationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ReservationServiceImpl.class);
    
    @Autowired
    private ReservationMapper reservationMapper;
    
    @Autowired
    private RoomMapper roomMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    /**
     * 自动删除剩余天数为0的用户的所有活跃预约
     */
    @Transactional
    public void deleteReservationsForUsersWithZeroDays() {
        // 找到剩余天数为0的用户
        QueryWrapper<User> userWrapper = new QueryWrapper<>();
        userWrapper.le("remaining_days", 0);
        List<User> usersWithZeroDays = userMapper.selectList(userWrapper);

        int deletedCount = 0;
        for (User user : usersWithZeroDays) {
            // 删除该用户的所有活跃预约
            QueryWrapper<Reservation> reservationWrapper = new QueryWrapper<>();
            reservationWrapper.eq("user_id", user.getId())
                             .eq("status", "ACTIVE");

            List<Reservation> activeReservations = reservationMapper.selectList(reservationWrapper);
            for (Reservation reservation : activeReservations) {
                reservationMapper.deleteById(reservation.getId());
                deletedCount++;
                logger.info("删除用户{}的预约记录，预约ID: {}, 座位: {}",
                           user.getUsername(), reservation.getId(), reservation.getSeatId());
            }
        }

        if (deletedCount > 0) {
            logger.info("共删除了{}个剩余天数为0的用户的预约记录", deletedCount);
        }
    }

    /**
     * 立即检查并删除指定用户的预约（如果剩余天数为0）
     * @param userId 用户ID
     */
    @Transactional
    public void checkAndDeleteUserReservationsIfZeroDays(Long userId) {
        try {
            // 查询用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                return;
            }

            // 如果剩余天数为0，删除其所有活跃预约
            if (user.getRemainingDays() <= 0) {
                QueryWrapper<Reservation> reservationWrapper = new QueryWrapper<>();
                reservationWrapper.eq("user_id", userId)
                                 .eq("status", "ACTIVE");

                List<Reservation> activeReservations = reservationMapper.selectList(reservationWrapper);
                for (Reservation reservation : activeReservations) {
                    reservationMapper.deleteById(reservation.getId());
                    logger.info("立即删除用户{}的预约记录，预约ID: {}, 座位: {}",
                               user.getUsername(), reservation.getId(), reservation.getSeatId());
                }

                if (!activeReservations.isEmpty()) {
                    logger.info("用户{}剩余天数为0，立即删除了{}个预约记录",
                               user.getUsername(), activeReservations.size());
                }
            }
        } catch (Exception e) {
            logger.error("检查并删除用户{}预约时发生错误: {}", userId, e.getMessage(), e);
        }
    }

    /**
     * 注意：由于现在采用删除策略而不是暂停策略，
     * 不再需要恢复预约的方法，因为删除的预约无法恢复。
     * 用户需要重新创建预约。
     */
    
    /**
     * 清理用户的重复预约，确保每个用户只有一个活跃预约
     * @param userId 用户ID
     */
    @Transactional
    private void cleanupDuplicateReservations(Long userId) {
        // 查找用户的所有活跃预约
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId)
               .eq("status", "ACTIVE")
               .orderByDesc("created_time"); // 按创建时间降序，保留最新的
        
        List<Reservation> activeReservations = reservationMapper.selectList(wrapper);
        
        // 如果有多个活跃预约，取消除最新的以外的所有预约
        if (activeReservations.size() > 1) {
            for (int i = 1; i < activeReservations.size(); i++) {
                Reservation oldReservation = activeReservations.get(i);
                oldReservation.setStatus("CANCELLED");
                reservationMapper.updateById(oldReservation);
            }
        }
    }
    
    @Override
    @Transactional
    public Reservation createOrUpdateReservation(Long userId, Long roomId, String seatId, 
                                               LocalTime startTime, LocalTime endTime, 
                                               String reservationOpenTime, String reservationType) {
        try {
            logger.info("开始处理预约请求: 用户ID={}, 房间ID={}, 座位号={}, 时间=[{}-{}]", 
                       userId, roomId, seatId, startTime, endTime);
            
            // 1. 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }

            // 检查用户剩余天数，剩余天数为0时不允许预约
            if (user.getRemainingDays() <= 0) {
                logger.warn("用户{}剩余天数为{}，不允许预约", userId, user.getRemainingDays());
                throw new RuntimeException("您的剩余天数不足，无法进行预约。请联系管理员充值。");
            } else {
                logger.info("用户{}剩余天数为{}，状态正常", userId, user.getRemainingDays());
            }
            
            // 2. 检查房间是否存在
            Room room = roomMapper.selectById(roomId);
            if (room == null) {
                throw new RuntimeException("房间不存在");
            }
            
            // 3. 检查时间间隔是否至少15分钟
            long diffMinutes = java.time.Duration.between(startTime, endTime).toMinutes();
            if (diffMinutes <= 0) {
                // 处理跨午夜的情况
                diffMinutes = java.time.Duration.between(startTime, endTime.plusHours(24)).toMinutes();
            }
            if (diffMinutes < 15) {
                throw new RuntimeException("预约时间间隔至少需要15分钟");
            }
            
            // 4. 查找用户的现有活跃预约
            QueryWrapper<Reservation> userWrapper = new QueryWrapper<>();
            userWrapper.eq("user_id", userId)
                      .eq("status", "ACTIVE")
                      .orderByDesc("created_time");
            
            List<Reservation> userReservations = reservationMapper.selectList(userWrapper);
            logger.info("用户{}当前有{}个活跃预约", userId, userReservations.size());
            
            // 5. 清理用户多个活跃预约，只保留最新的
            Reservation currentReservation = null;
            if (!userReservations.isEmpty()) {
                currentReservation = userReservations.get(0);
                // 取消其他所有活跃预约
                for (int i = 1; i < userReservations.size(); i++) {
                    Reservation oldReservation = userReservations.get(i);
                    oldReservation.setStatus("CANCELLED");
                    reservationMapper.updateById(oldReservation);
                    logger.info("取消重复预约: ID={}", oldReservation.getId());
                }
            }
            
            // 6. 检查目标座位是否被其他用户占用（排除当前用户的预约）
            QueryWrapper<Reservation> seatConflictWrapper = new QueryWrapper<>();
            seatConflictWrapper.eq("room_id", roomId)
                              .eq("seat_id", seatId)
                              .eq("status", "ACTIVE")
                              .ne("user_id", userId); // 排除当前用户

            // 如果是更新操作，还要排除当前用户的现有预约
            if (currentReservation != null) {
                seatConflictWrapper.ne("id", currentReservation.getId());
            }

            // 添加更详细的日志，记录SQL查询条件
            logger.info("座位冲突查询条件: room_id={}, seat_id={}, status='ACTIVE', user_id!={}",
                      roomId, seatId, userId);

            // 检查数据库中是否存在任何与该座位相关的记录（不限状态）
            QueryWrapper<Reservation> allSeatRecordsWrapper = new QueryWrapper<>();
            allSeatRecordsWrapper.eq("room_id", roomId).eq("seat_id", seatId);
            List<Reservation> allSeatRecords = reservationMapper.selectList(allSeatRecordsWrapper);
            logger.info("座位{}在房间{}共有{}条记录（所有状态）", seatId, roomId, allSeatRecords.size());
            for (Reservation record : allSeatRecords) {
                logger.info("座位记录: ID={}, 用户ID={}, 状态={}, 创建时间={}",
                          record.getId(), record.getUserId(), record.getStatus(), record.getCreatedTime());
            }

            List<Reservation> seatConflictReservations = reservationMapper.selectList(seatConflictWrapper);
            logger.info("座位{}冲突检查结果: 房间{}, 排除用户{}, 找到{}个其他用户的活跃预约",
                        seatId, roomId, userId, seatConflictReservations.size());

            // 打印详细的冲突预约信息
            for (Reservation conflict : seatConflictReservations) {
                logger.info("发现其他用户预约: ID={}, 用户ID={}, 座位={}, 时间=[{}-{}], 状态={}",
                           conflict.getId(), conflict.getUserId(), conflict.getSeatId(),
                           conflict.getStartTime(), conflict.getEndTime(), conflict.getStatus());
            }

            // 检查时间冲突：只有时间重叠的预约才算冲突
            List<Reservation> timeConflictReservations = seatConflictReservations.stream()
                .filter(reservation -> isTimeConflict(startTime, endTime,
                        reservation.getStartTime(), reservation.getEndTime()))
                .collect(java.util.stream.Collectors.toList());

            logger.info("座位{}时间冲突检查结果: 请求时间[{}-{}], 发现{}个时间冲突的预约",
                        seatId, startTime, endTime, timeConflictReservations.size());

            // 打印时间冲突的预约详情
            for (Reservation conflict : timeConflictReservations) {
                logger.warn("时间冲突预约: ID={}, 用户ID={}, 时间=[{}-{}]",
                           conflict.getId(), conflict.getUserId(),
                           conflict.getStartTime(), conflict.getEndTime());
            }

            // 检查是否确实存在时间冲突
            if (!timeConflictReservations.isEmpty()) {
                logger.warn("座位{}在时间段[{}-{}]已被其他用户预约，拒绝本次预约请求",
                           seatId, startTime, endTime);
                throw new RuntimeException("座位 " + seatId + " 在请求的时间段已被其他用户预约，请选择其他时间或座位");
            } else {
                logger.info("座位{}在时间段[{}-{}]没有冲突，可以继续预约流程", seatId, startTime, endTime);
            }
            
            // 7. 执行创建或更新操作
            Reservation reservation;
            boolean isNewReservation = (currentReservation == null);
            
            if (currentReservation != null) {
                // 更新现有预约
                reservation = currentReservation;
                logger.info("更新现有预约: ID={}, 旧座位={}, 新座位={}", 
                           reservation.getId(), reservation.getSeatId(), seatId);
                
                // 更新预约信息
                reservation.setRoomId(roomId);
                reservation.setSeatId(seatId);
                reservation.setStartTime(startTime);
                reservation.setEndTime(endTime);
                reservation.setReservationOpenTime(reservationOpenTime);
                reservation.setReservationType(reservationType);
                
                try {
                    int updateResult = reservationMapper.updateById(reservation);
                    if (updateResult == 0) {
                        throw new RuntimeException("更新预约失败");
                    }
                    logger.info("预约更新成功: ID={}", reservation.getId());
                } catch (Exception e) {
                    logger.error("更新预约时发生异常: {}", e.getMessage(), e);
                    // 检查是否是唯一约束冲突
                    if (e.getMessage() != null && (e.getMessage().contains("unique_active_seat_reservation") || 
                        e.getMessage().contains("Duplicate entry"))) {
                        
                        // 再次查询该座位的所有记录，包括可能在此期间被其他用户创建的记录
                        QueryWrapper<Reservation> finalCheckWrapper = new QueryWrapper<>();
                        finalCheckWrapper.eq("room_id", roomId)
                                       .eq("seat_id", seatId)
                                       .eq("status", "ACTIVE")
                                       .ne("id", reservation.getId());
                        
                        List<Reservation> finalConflicts = reservationMapper.selectList(finalCheckWrapper);
                        if (!finalConflicts.isEmpty()) {
                            logger.warn("最终检查发现座位冲突: 房间={}, 座位={}, 冲突数量={}", 
                                      roomId, seatId, finalConflicts.size());
                            for (Reservation conflict : finalConflicts) {
                                logger.info("最终冲突: ID={}, 用户ID={}", conflict.getId(), conflict.getUserId());
                            }
                        } else {
                            logger.warn("数据库约束冲突，但查询未发现冲突记录，可能存在脏数据或事务问题");
                        }
                        
                        throw new RuntimeException("座位 " + seatId + " 已被其他用户预约，请选择其他座位");
                    }
                    throw e; // 重新抛出其他类型的异常
                }
            } else {
                // 创建新预约
                logger.info("创建新预约: 用户ID={}, 座位={}", userId, seatId);
                reservation = new Reservation();
                reservation.setUserId(userId);
                reservation.setRoomId(roomId);
                reservation.setSeatId(seatId);
                reservation.setStartTime(startTime);
                reservation.setEndTime(endTime);
                reservation.setStatus("ACTIVE");
                reservation.setReservationOpenTime(reservationOpenTime);
                reservation.setReservationType(reservationType);
                reservation.setCreatedTime(LocalDateTime.now());
                
                try {
                    int insertResult = reservationMapper.insert(reservation);
                    if (insertResult == 0) {
                        throw new RuntimeException("创建预约失败");
                    }
                    logger.info("新预约创建成功: ID={}", reservation.getId());
                } catch (Exception e) {
                    logger.error("创建预约时发生异常: {}", e.getMessage(), e);
                    // 检查是否是唯一约束冲突
                    if (e.getMessage() != null && (e.getMessage().contains("unique_active_seat_reservation") || 
                        e.getMessage().contains("Duplicate entry"))) {
                        
                        // 再次查询该座位的所有记录，查看是否真的有冲突
                        QueryWrapper<Reservation> finalCheckWrapper = new QueryWrapper<>();
                        finalCheckWrapper.eq("room_id", roomId)
                                       .eq("seat_id", seatId)
                                       .eq("status", "ACTIVE");
                        
                        List<Reservation> finalConflicts = reservationMapper.selectList(finalCheckWrapper);
                        if (!finalConflicts.isEmpty()) {
                            logger.warn("最终检查发现座位冲突: 房间={}, 座位={}, 冲突数量={}", 
                                      roomId, seatId, finalConflicts.size());
                            for (Reservation conflict : finalConflicts) {
                                logger.info("最终冲突: ID={}, 用户ID={}", conflict.getId(), conflict.getUserId());
                            }
                        } else {
                            logger.warn("数据库约束冲突，但查询未发现冲突记录，可能存在脏数据或事务问题");
                        }
                        
                        throw new RuntimeException("座位 " + seatId + " 已被其他用户预约，请选择其他座位");
                    }
                    throw e; // 重新抛出其他类型的异常
                }
            }
            
            // 8. 如果是新预约，扣减用户剩余天数（只有剩余天数大于0时才扣减）
            if (isNewReservation) {
                if (user.getRemainingDays() > 0) {
                    user.setRemainingDays(user.getRemainingDays() - 1);
                    userMapper.updateById(user);
                    logger.info("扣减用户{}剩余天数，当前剩余: {}", userId, user.getRemainingDays());
                } else {
                    logger.info("用户{}剩余天数为{}，不扣减天数，状态为暂停", userId, user.getRemainingDays());
                }
            }
            
            // 9. 最终验证：确保用户只有一个活跃预约
            QueryWrapper<Reservation> finalCheckWrapper = new QueryWrapper<>();
            finalCheckWrapper.eq("user_id", userId).eq("status", "ACTIVE");
            long activeCount = reservationMapper.selectCount(finalCheckWrapper);
            if (activeCount > 1) {
                logger.error("最终验证失败：用户{}存在{}个活跃预约", userId, activeCount);
                throw new RuntimeException("系统错误：用户存在多个活跃预约");
            }
            
            logger.info("预约处理完成: 用户ID={}, 座位={}, 预约ID={}", userId, seatId, reservation.getId());
            return reservation;
        } catch (Exception e) {
            logger.error("预约处理异常: 用户ID={}, 座位={}, 异常信息: {}", userId, seatId, e.getMessage(), e);
            
            // 检查数据库约束异常
            if (e.getMessage() != null) {
                if (e.getMessage().contains("unique_active_seat_reservation") || 
                    e.getMessage().contains("Duplicate entry")) {
                    logger.warn("数据库约束触发: 座位{}已被占用", seatId);
                    
                    // 再次查询该座位的所有记录，查看是否真的有冲突
                    QueryWrapper<Reservation> finalCheckWrapper = new QueryWrapper<>();
                    finalCheckWrapper.eq("room_id", roomId)
                                   .eq("seat_id", seatId)
                                   .eq("status", "ACTIVE");
                    
                    List<Reservation> finalConflicts = reservationMapper.selectList(finalCheckWrapper);
                    if (!finalConflicts.isEmpty()) {
                        logger.warn("最终检查发现座位冲突: 房间={}, 座位={}, 冲突数量={}", 
                                  roomId, seatId, finalConflicts.size());
                        for (Reservation conflict : finalConflicts) {
                            logger.info("最终冲突: ID={}, 用户ID={}", conflict.getId(), conflict.getUserId());
                        }
                    } else {
                        logger.warn("数据库约束冲突，但查询未发现冲突记录，可能存在脏数据或事务问题");
                    }
                    
                    throw new RuntimeException("座位 " + seatId + " 已被其他用户预约，请选择其他座位");
                }
                if (e.getMessage().contains("座位已被其他用户预约")) {
                    logger.warn("触发器约束触发: 座位{}已被占用", seatId);
                    throw e; // 直接重新抛出这个异常
                }
            }
            
            // 重新抛出其他异常
            throw e;
        }
    }
    
    /**
     * 检查两个时间段是否冲突
     * @param start1 时间段1开始时间
     * @param end1 时间段1结束时间
     * @param start2 时间段2开始时间
     * @param end2 时间段2结束时间
     * @return 是否冲突
     */
    private boolean isTimeConflict(LocalTime start1, LocalTime end1, LocalTime start2, LocalTime end2) {
        // 修复：正确的时间冲突检查逻辑
        // 两个时间段冲突的条件：max(start1, start2) < min(end1, end2)
        LocalTime maxStart = start1.isAfter(start2) ? start1 : start2;
        LocalTime minEnd = end1.isBefore(end2) ? end1 : end2;
        
        boolean conflict = maxStart.isBefore(minEnd);
        
        // 添加调试日志
        logger.debug("时间冲突检查: [{}-{}] vs [{}-{}] = {}", 
                    start1, end1, start2, end2, conflict);
        
        return conflict;
    }
    
    @Override
    @Transactional
    public boolean cancelReservation(Long reservationId, Long userId) {
        // 1. 查找预约记录
        Reservation reservation = reservationMapper.selectById(reservationId);
        if (reservation == null || !reservation.getUserId().equals(userId)) {
            return false;
        }
        
        // 2. 取消预约（移除时间检查，因为现在只存储时间不存储日期）
        reservation.setStatus("CANCELLED");
        return reservationMapper.updateById(reservation) > 0;
    }
    
    @Override
    public UserProfileResponse.CurrentReservation getCurrentReservation(Long userId) {
        return reservationMapper.getCurrentReservationByUserId(userId);
    }
    
    @Override
    public int getAvailableSeatsCount(Long roomId, LocalTime startTime, LocalTime endTime) {
        // 1. 检查房间是否存在
        Room room = roomMapper.selectById(roomId);
        if (room == null) {
            return 0;
        }

        // 2. 查询该时间段已预约的座位数（只计算ACTIVE状态的预约）
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("room_id", roomId)
               .eq("status", "ACTIVE"); // 只计算活跃预约

        List<Reservation> activeReservations = reservationMapper.selectList(wrapper);

        // 检查时间冲突的预约数量
        long conflictCount = activeReservations.stream()
                .mapToLong(reservation -> isTimeConflict(startTime, endTime,
                        reservation.getStartTime(), reservation.getEndTime()) ? 1 : 0)
                .sum();

        // 3. 由于不再维护总座位数，返回一个基于冲突数的可用座位估算
        // 假设房间有足够的座位，返回一个合理的可用座位数
        int estimatedTotalSeats = 100; // 假设每个房间有100个座位
        return Math.max(0, estimatedTotalSeats - (int)conflictCount);
    }
    
    @Override
    public List<Reservation> getActiveReservations(Long roomId) {
        QueryWrapper<Reservation> wrapper = new QueryWrapper<>();
        wrapper.eq("room_id", roomId)
               .eq("status", "ACTIVE")
               .orderByAsc("start_time");
        
        return reservationMapper.selectList(wrapper);
    }
} 