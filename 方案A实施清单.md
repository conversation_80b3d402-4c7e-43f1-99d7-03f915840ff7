# 方案A：Java重构实施清单

## 📋 实施步骤清单

### 第1天：数据库准备
- [ ] **1.1 执行数据库扩展脚本**
```sql
-- 在MySQL中执行以下SQL
ALTER TABLE reservations ADD COLUMN auto_reservation_config TEXT COMMENT '自动预约配置(JSON)';
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

CREATE TABLE auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',
    
    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

- [ ] **1.2 验证数据库更改**
```sql
-- 检查表结构
DESCRIBE reservations;
DESCRIBE auto_execution_logs;
```

### 第2天：创建核心实体类
- [ ] **2.1 创建 AutoExecutionLog.java**
  - 位置：`backend/src/main/java/com/seatmaster/entity/AutoExecutionLog.java`
  - 复制完整代码到文件

- [ ] **2.2 创建 ExecutionResult.java**
  - 位置：`backend/src/main/java/com/seatmaster/dto/ExecutionResult.java`
  - 复制完整代码到文件

- [ ] **2.3 创建 ReservationConfig.java**
  - 位置：`backend/src/main/java/com/seatmaster/dto/ReservationConfig.java`
  - 复制完整代码到文件

- [ ] **2.4 创建 AutoReservationRequest.java**
  - 位置：`backend/src/main/java/com/seatmaster/dto/AutoReservationRequest.java`
  - 复制完整代码到文件

### 第3天：创建核心服务类
- [ ] **3.1 创建 XuexitongApiService.java**
  - 位置：`backend/src/main/java/com/seatmaster/service/XuexitongApiService.java`
  - 复制完整代码到文件
  - ⚠️ **重要**：这是核心预约逻辑，需要仔细检查

- [ ] **3.2 创建 AutoReservationSchedulerService.java**
  - 位置：`backend/src/main/java/com/seatmaster/service/AutoReservationSchedulerService.java`
  - 复制完整代码到文件

- [ ] **3.3 创建 AutoExecutionLogMapper.java**
  - 位置：`backend/src/main/java/com/seatmaster/mapper/AutoExecutionLogMapper.java`
  - 复制完整代码到文件

### 第4天：扩展现有Controller
- [ ] **4.1 修改 ReservationController.java**
  - 在现有文件中添加自动预约相关方法
  - 添加必要的依赖注入
  - 测试编译是否通过

- [ ] **4.2 创建 HttpClientConfig.java**
  - 位置：`backend/src/main/java/com/seatmaster/config/HttpClientConfig.java`
  - 配置RestTemplate Bean

### 第5天：配置和测试
- [ ] **5.1 更新 application.yml**
```yaml
# 添加以下配置到现有application.yml
xuexitong:
  base-url: "https://passport2.chaoxing.com"
  office-url: "https://office.chaoxing.com"
  timeout: 30000
  retry-count: 3

auto-reservation:
  enabled: true
  scheduler:
    enabled: true
    max-concurrent: 5

spring:
  task:
    scheduling:
      enabled: true
      pool:
        size: 10

logging:
  level:
    com.seatmaster.service.XuexitongApiService: DEBUG
    com.seatmaster.service.AutoReservationSchedulerService: DEBUG
```

- [ ] **5.2 编译测试**
```bash
cd backend
mvn clean compile
```

- [ ] **5.3 启动应用测试**
```bash
mvn spring-boot:run
```

### 第6天：功能测试
- [ ] **6.1 测试创建自动预约**
  - 使用Postman或curl测试API
  - 检查数据库中是否正确创建记录

- [ ] **6.2 测试定时任务**
  - 设置一个即将到达的预约时间
  - 观察日志输出
  - 检查是否正确执行

- [ ] **6.3 测试手动执行**
  - 调用手动执行API
  - 检查执行结果和日志

### 第7天：优化和部署
- [ ] **7.1 性能优化**
  - 调整并发数量
  - 优化日志输出
  - 处理异常情况

- [ ] **7.2 生产部署**
  - 打包应用：`mvn clean package -DskipTests`
  - 部署到生产环境
  - 监控运行状态

## 🔧 关键检查点

### 检查点1：数据库连接
```sql
-- 确认能正常查询新表
SELECT COUNT(*) FROM auto_execution_logs;
```

### 检查点2：应用启动
```bash
# 启动时应该看到类似日志
2024-01-15 10:00:00 INFO  - 开始扫描自动预约任务...
```

### 检查点3：API测试
```bash
# 测试健康检查
curl http://localhost:8080/actuator/health

# 测试自动预约API
curl -X GET http://localhost:8080/api/reservations/auto \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 检查点4：定时任务
```bash
# 查看定时任务日志
tail -f logs/application.log | grep "扫描自动预约"
```

## ⚠️ 常见问题和解决方案

### 问题1：编译错误
**现象**：找不到某些类或方法
**解决**：
1. 检查import语句是否正确
2. 确认所有依赖的类都已创建
3. 重新编译：`mvn clean compile`

### 问题2：数据库连接错误
**现象**：启动时报数据库相关错误
**解决**：
1. 检查数据库连接配置
2. 确认数据库表已正确创建
3. 检查用户权限

### 问题3：定时任务不执行
**现象**：看不到定时任务日志
**解决**：
1. 检查`@EnableScheduling`注解
2. 确认cron表达式正确
3. 检查应用配置中的scheduling.enabled

### 问题4：学习通登录失败
**现象**：预约时提示登录失败
**解决**：
1. 检查用户名密码是否正确
2. 确认AES加密逻辑正确
3. 检查网络连接和URL配置

## 📊 预期效果

实施完成后，您应该能看到：

### 性能提升
- 单次预约耗时：从2.5-5.5秒降低到0.5-2秒
- 并发处理：从1个提升到5个
- 整体效率：提升2-5倍

### 功能增强
- 用户可以在Web界面创建自动预约
- 系统自动在指定时间执行预约
- 完整的执行日志和状态跟踪
- 支持手动触发执行

### 系统稳定性
- 统一的错误处理机制
- 完整的日志记录
- 自动重试机制
- 优雅的异常处理

## 🎯 下一步计划

方案A实施完成后，您可以考虑：

1. **监控优化**：添加更详细的性能监控
2. **功能扩展**：支持更多预约策略
3. **用户体验**：优化前端界面
4. **方案B升级**：如果需要更高性能，可以升级到分布式版本

祝您实施顺利！有任何问题随时联系我。
