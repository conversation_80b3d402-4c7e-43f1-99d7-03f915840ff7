# 极简数据库设计说明

## 1. 设计原则

基于您的观察，我们采用**极简数据库设计**：

### 1.1 现有字段充分利用
```sql
-- 现有reservations表字段利用：
-- user_id → 关联users表获取username/password
-- room_id → 目标房间ID
-- seat_id → 目标座位ID  
-- start_time/end_time → 预约时间段
-- reservation_open_time → 预约执行时间 (如"08:00:00")
-- reservation_type → 使用"ADVANCE_ONE_DAY"
-- status → 新增自动预约状态
-- created_time → 创建时间

-- 现有users表字段利用：
-- username → 直接用于学习通登录
-- password → 直接用于学习通登录  
-- remaining_days → 控制自动预约启用状态
```

### 1.2 最小化新增字段
```sql
-- 仅需添加2个字段到reservations表
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 扩展status枚举
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';
```

## 2. 为什么不需要auto_reservation_config字段

### 2.1 配置信息很简单
```java
// 自动预约需要的信息：
- 用户名密码：直接从users表获取
- 房间座位：直接从reservations表获取
- 执行时间：直接从reservation_open_time获取
- 重试配置：可以写在代码中作为常量

// 不需要复杂的JSON配置
```

### 2.2 简化的业务逻辑
```java
// 执行自动预约的逻辑：
1. 查询status='AUTO_PENDING'的预约记录
2. 检查reservation_open_time是否到达
3. 从users表获取username/password
4. 调用学习通API执行预约
5. 更新status和execution_result

// 整个流程不需要额外配置
```

## 3. 简化后的数据流

### 3.1 创建自动预约
```java
// 用户创建自动预约时
Reservation reservation = new Reservation();
reservation.setUserId(userId);              // 用户ID
reservation.setRoomId(roomId);              // 房间ID
reservation.setSeatId(seatId);              // 座位ID
reservation.setStartTime(startTime);        // 开始时间
reservation.setEndTime(endTime);            // 结束时间
reservation.setReservationOpenTime(openTime); // 执行时间
reservation.setReservationType("ADVANCE_ONE_DAY");
reservation.setStatus("AUTO_PENDING");      // 等待执行

// 不需要存储任何配置信息！
```

### 3.2 执行自动预约
```java
// 定时任务执行时
1. 查询待执行预约：
   SELECT * FROM reservations 
   WHERE status = 'AUTO_PENDING' 
   AND reservation_open_time = CURTIME()

2. 获取用户信息：
   SELECT username, password FROM users WHERE id = reservation.user_id

3. 执行预约：
   xuexitongApi.login(user.username, user.password)
   xuexitongApi.reserveSeat(reservation.roomId, reservation.seatId)

4. 更新结果：
   UPDATE reservations SET 
   status = 'AUTO_SUCCESS',
   execution_result = '{"message":"预约成功"}',
   last_execution_time = NOW()
```

## 4. 简化后的代码变更

### 4.1 不需要的类
```java
// 删除这些复杂的配置类：
❌ ReservationConfig.java (不需要)
❌ 复杂的配置解析逻辑
❌ JSON配置存储和读取

// 保留简单的类：
✅ ExecutionResult.java (执行结果)
✅ AutoExecutionLog.java (执行日志)
✅ AutoReservationRequest.java (简化版)
```

### 4.2 简化的请求DTO
```java
@Data
public class AutoReservationRequest {
    @NotNull private Long roomId;
    @NotBlank private String seatId;
    @NotBlank private String startTime;
    @NotBlank private String endTime;
    @NotBlank private String reservationOpenTime;
    
    // 不需要config字段！
}
```

### 4.3 简化的Service方法
```java
public ExecutionResult executeReservation(Reservation reservation) {
    // 1. 获取用户信息 (直接从users表)
    User user = userService.getById(reservation.getUserId());
    
    // 2. 使用users表的用户名密码登录学习通
    boolean loginSuccess = xuexitongSession.login(user.getUsername(), user.getPassword());
    
    // 3. 使用reservations表的房间座位信息预约
    boolean reserveSuccess = xuexitongSession.reserveSeat(
        reservation.getRoomId(), 
        reservation.getSeatId(),
        reservation.getStartTime(),
        reservation.getEndTime()
    );
    
    // 不需要解析任何配置！
}
```

## 5. 最终的数据库脚本

```sql
-- 极简数据库更新脚本
USE seatmaster;

-- 1. 添加必要字段到reservations表
ALTER TABLE reservations ADD COLUMN last_execution_time TIMESTAMP NULL COMMENT '最后执行时间';
ALTER TABLE reservations ADD COLUMN execution_result TEXT COMMENT '执行结果(JSON)';

-- 2. 扩展status枚举
ALTER TABLE reservations MODIFY status ENUM('ACTIVE', 'CANCELLED', 'PAUSED', 'AUTO_PENDING', 'AUTO_SUCCESS', 'AUTO_FAILED') NOT NULL DEFAULT 'ACTIVE';

-- 3. 创建执行日志表
CREATE TABLE auto_execution_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    execution_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '执行时间',
    execution_status ENUM('SUCCESS', 'FAILED', 'TIMEOUT') NOT NULL,
    result_message TEXT COMMENT '执行结果信息',
    execution_duration INT COMMENT '执行耗时(秒)',
    
    INDEX idx_reservation_time (reservation_id, execution_time),
    INDEX idx_user_status (user_id, execution_status),
    FOREIGN KEY (reservation_id) REFERENCES reservations(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 4. 验证更改
DESCRIBE reservations;
DESCRIBE auto_execution_logs;

-- 完成！只添加了2个字段和1个日志表
```

## 6. 优势总结

### 6.1 极简设计的优势
- ✅ **数据库变更最小**：只添加2个字段
- ✅ **代码逻辑简单**：不需要复杂的配置解析
- ✅ **维护成本低**：没有复杂的JSON配置
- ✅ **性能更好**：减少数据库存储和查询开销
- ✅ **易于理解**：业务逻辑清晰直观

### 6.2 满足所有需求
- ✅ **自动预约**：定时执行预约任务
- ✅ **用户隔离**：每个用户独立的预约记录
- ✅ **状态跟踪**：完整的执行状态管理
- ✅ **日志记录**：详细的执行日志
- ✅ **错误处理**：完善的异常处理机制

这个极简设计完全满足您的需求，而且实施起来更加简单快捷！
